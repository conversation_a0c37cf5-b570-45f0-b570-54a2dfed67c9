import { create } from 'zustand';
import { apiClient } from '@/services/api';
import { convertUpdateDataToRoom } from '@/store/lobbyStore';
import type { Room, CreateRoomRequest, GetRoomsRequest, RoomsResponse } from '@/types/api';
import type {
  RoomListUpdateData,
  ColorStateUpdateData,
  RoomUpdateSpecData,
  BalanceUpdateData
} from '@/types/socket';

interface GameState {
  // Rooms
  rooms: Room[];
  roomsLoading: boolean;
  roomsError: string | null;
  roomsPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;

  // Current room
  currentRoom: Room | null;
  currentRoomLoading: boolean;
  currentRoomError: string | null;

  // Player ready state
  playerReadyStates: Record<string, boolean>; // playerId -> isReady
  readyStateLoading: boolean;

  // Prize Wheel color selections
  playerColorSelections: Record<string, string>; // playerId -> colorId
  colorSelectionLoading: boolean;

  // Amidakuji position selections
  playerPositionSelections: Record<string, number>; // playerId -> position
  positionSelectionLoading: boolean;

  // Real-time room data
  realTimeRoomData: {
    participantCount?: number;
    prizePool?: number;
    playerBalances: Record<string, number>; // playerId -> balance
    playerInsufficientBalance: Record<string, boolean>; // playerId -> insufficient balance status
    colorState?: {
      playerColors: Record<string, string>; // playerId -> colorId
      availableColors: string[];
      takenColors: Record<string, boolean>;
    };
    positionState?: {
      playerPositions: Record<string, number>; // playerId -> position
      availablePositions: number[];
      takenPositions: Record<number, boolean>;
    };
    amidakujiPattern?: any;
    amidakujiPathTracing?: any;
  };

  // Filters
  filters: {
    gameType?: string;
    status?: string;
    minBet?: number;
    maxBet?: number;
  };

  // Actions
  loadRooms: (params?: GetRoomsRequest) => Promise<void>;
  createRoom: (roomData: CreateRoomRequest) => Promise<Room>;
  loadRoomDetails: (roomId: string) => Promise<void>;
  joinRoom: (roomId: string, password?: string) => Promise<void>;
  leaveRoom: (roomId: string) => Promise<void>;
  setFilters: (filters: Partial<GameState['filters']>) => void;
  clearCurrentRoom: () => void;
  clearError: () => void;
  clearGameState: () => void;

  // Ready state actions
  updatePlayerReadyState: (playerId: string, isReady: boolean) => void;
  clearPlayerReadyStates: () => void;
  getPlayerReadyState: (playerId: string) => boolean;

  // Color selection actions
  updatePlayerColorSelection: (playerId: string, colorId: string) => void;
  clearPlayerColorSelections: () => void;
  getPlayerColorSelection: (playerId: string) => string | undefined;
  getAvailableColors: () => string[]; // Returns available color IDs

  // Real-time updates
  handleRoomListUpdate: (data: RoomListUpdateData) => void;
  handleColorStateUpdate: (data: ColorStateUpdateData) => void;
  handleRoomUpdateSpec: (data: RoomUpdateSpecData) => void;
  handleBalanceUpdate: (data: BalanceUpdateData) => void;
  updateRealTimeRoomData: (data: Partial<GameState['realTimeRoomData']>) => void;

  // Enhanced data getters
  getPlayerBalance: (playerId: string) => number | undefined;
  getPlayerInsufficientBalance: (playerId: string) => boolean;
  getRealTimeParticipantCount: () => number | undefined;
  getRealTimePrizePool: () => number | undefined;
  getRealTimeColorState: () => { playerColors: Record<string, string>; availableColors: string[]; takenColors: Record<string, boolean> } | undefined;

  // Amidakuji position management
  updatePlayerPositionSelection: (playerId: string, position: number) => void;
  updateRealTimePositionState: (data: any) => void;
  getRealTimePositionState: () => any;

  // Amidakuji game data management
  updateAmidakujiPattern: (data: any) => void;
  updateAmidakujiPathTracing: (data: any) => void;
  getAmidakujiPattern: () => any;
  getAmidakujiPathTracing: () => any;
}

export const useGameStore = create<GameState>((set, get) => ({
  // Initial state
  rooms: [],
  roomsLoading: false,
  roomsError: null,
  roomsPagination: null,
  currentRoom: null,
  currentRoomLoading: false,
  currentRoomError: null,
  playerReadyStates: {},
  readyStateLoading: false,
  playerColorSelections: {},
  colorSelectionLoading: false,
  playerPositionSelections: {},
  positionSelectionLoading: false,
  realTimeRoomData: {
    playerBalances: {},
    playerInsufficientBalance: {},
  },
  filters: {},

  // Actions
  loadRooms: async (params: GetRoomsRequest = {}) => {
    set({ roomsLoading: true, roomsError: null });

    try {
      const { filters } = get();
      const requestParams = { ...filters, ...params };
      const response: RoomsResponse = await apiClient.getRooms(requestParams);

      set({
        rooms: response.rooms,
        roomsPagination: response.pagination,
        roomsLoading: false,
        roomsError: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load rooms';
      set({
        rooms: [],
        roomsPagination: null,
        roomsLoading: false,
        roomsError: errorMessage,
      });
      throw error;
    }
  },

  createRoom: async (roomData: CreateRoomRequest) => {
    try {
      const room = await apiClient.createRoom(roomData);

      // Add the new room to the list
      set((state) => ({
        rooms: [room, ...state.rooms],
      }));

      return room;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create room';
      set({ roomsError: errorMessage });
      throw error;
    }
  },

  loadRoomDetails: async (roomId: string) => {
    set({ currentRoomLoading: true, currentRoomError: null });

    try {
      const room = await apiClient.getRoomDetails(roomId);
      set({
        currentRoom: room,
        currentRoomLoading: false,
        currentRoomError: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load room details';
      set({
        currentRoom: null,
        currentRoomLoading: false,
        currentRoomError: errorMessage,
      });
      throw error;
    }
  },

  joinRoom: async (roomId: string, password?: string) => {
    try {
      const { room } = await apiClient.joinRoom(roomId, password);

      // Update current room
      set({ currentRoom: room });

      // Update room in the list if it exists
      set((state) => ({
        rooms: state.rooms.map(r => r.id === roomId ? room : r),
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to join room';
      set({ currentRoomError: errorMessage });
      throw error;
    }
  },

  leaveRoom: async (roomId: string) => {
    try {
      await apiClient.leaveRoom(roomId);

      // Clear current room if it's the one we left
      const { currentRoom } = get();
      if (currentRoom && currentRoom.id === roomId) {
        set({ currentRoom: null });
      }

      // Refresh rooms list to update player counts
      await get().loadRooms();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to leave room';
      set({ currentRoomError: errorMessage });
      throw error;
    }
  },

  setFilters: (newFilters: Partial<GameState['filters']>) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    }));
  },

  clearCurrentRoom: () => {
    set({
      currentRoom: null,
      currentRoomError: null,
      // Clear all player-related state when leaving room
      playerReadyStates: {},
      playerColorSelections: {},
      realTimeRoomData: {
        playerBalances: {},
        playerInsufficientBalance: {},
      },
    });
  },

  clearError: () => {
    set({ roomsError: null, currentRoomError: null });
  },

  // Clear all game-related state when leaving a room
  clearGameState: () => {
    set({
      playerReadyStates: {},
      playerColorSelections: {},
      readyStateLoading: false,
      colorSelectionLoading: false,
      realTimeRoomData: {
        playerBalances: {},
        playerInsufficientBalance: {},
      },
    });
  },

  // Ready state actions
  updatePlayerReadyState: (playerId: string, isReady: boolean) => {
    set((state) => ({
      playerReadyStates: {
        ...state.playerReadyStates,
        [playerId]: isReady,
      },
    }));
  },

  clearPlayerReadyStates: () => {
    set({ playerReadyStates: {} });
  },

  getPlayerReadyState: (playerId: string) => {
    const { playerReadyStates } = get();
    return playerReadyStates[playerId] || false;
  },

  // Color selection actions
  updatePlayerColorSelection: (playerId: string, colorId: string) => {
    set((state) => ({
      playerColorSelections: {
        ...state.playerColorSelections,
        [playerId]: colorId,
      },
    }));
  },

  clearPlayerColorSelections: () => {
    set({ playerColorSelections: {} });
  },

  getPlayerColorSelection: (playerId: string) => {
    const { playerColorSelections } = get();
    return playerColorSelections[playerId];
  },

  getAvailableColors: () => {
    const { playerColorSelections } = get();
    const selectedColors = Object.values(playerColorSelections);
    // This would typically come from a constants file
    const allColors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'teal'];
    return allColors.filter(color => !selectedColors.includes(color));
  },

  // Real-time updates
  handleRoomListUpdate: (data: RoomListUpdateData) => {
    set((state) => {
      switch (data.action) {
        case 'initial_load':
          // Replace entire room list with initial data
          const initialRooms = data.rooms.map(roomData => convertUpdateDataToRoom(roomData) as Room);
          return {
            rooms: initialRooms,
            roomsError: data.error || null,
          };

        case 'created': {
          // Add new room to the beginning of the list
          const newRoom = convertUpdateDataToRoom(data.room) as Room;
          return {
            rooms: [newRoom, ...state.rooms],
          };
        }

        case 'updated':
        case 'player_count_changed': {
          // Update existing room
          const rooms = [...state.rooms];
          const roomIndex = rooms.findIndex(r => r.id === data.room.id);

          if (roomIndex !== -1) {
            const updatedRoom = { ...rooms[roomIndex], ...convertUpdateDataToRoom(data.room) };
            rooms[roomIndex] = updatedRoom;

            // Also update current room if it's the same
            const currentRoom = state.currentRoom?.id === data.room.id
              ? { ...state.currentRoom, ...convertUpdateDataToRoom(data.room) }
              : state.currentRoom;

            return {
              rooms,
              currentRoom,
            };
          }
          break;
        }

        case 'deleted': {
          // Remove room from list
          const filteredRooms = state.rooms.filter(r => r.id !== data.room.id);

          // Clear current room if it's the deleted one
          const currentRoom = state.currentRoom?.id === data.room.id
            ? null
            : state.currentRoom;

          return {
            rooms: filteredRooms,
            currentRoom,
          };
        }

        case 'player_joined_room': {
          // Update room list when a player joins a room
          const updatedRooms = data.rooms.map(roomData => convertUpdateDataToRoom(roomData) as Room);

          // Find the joined room and set it as current room if it matches
          const joinedRoom = updatedRooms.find(room => room.id === data.joinedRoomId);

          return {
            rooms: updatedRooms,
            currentRoom: joinedRoom || state.currentRoom,
          };
        }

        case 'subscribe':
        case 'unsubscribe': {
          // Handle lobby subscription events with room list updates
          if (data.rooms && Array.isArray(data.rooms)) {
            const subscriptionRooms = data.rooms.map(roomData => convertUpdateDataToRoom(roomData) as Room);

            return {
              rooms: subscriptionRooms,
              roomsError: data.error || null,
            };
          }

          // If no rooms data, just clear any previous error
          return {
            roomsError: data.error || null,
          };
        }

        default:
          console.warn('Unknown room list update action:', data);
      }

      return state;
    });
  },

  // Enhanced real-time data handlers
  handleColorStateUpdate: (data: ColorStateUpdateData) => {
    set((state) => ({
      realTimeRoomData: {
        ...state.realTimeRoomData,
        colorState: {
          playerColors: data.playerColors,
          availableColors: data.availableColors,
          takenColors: data.takenColors,
        },
      },
      // Also update the legacy playerColorSelections for backward compatibility
      playerColorSelections: data.playerColors,
    }));
  },

  handleRoomUpdateSpec: (data: RoomUpdateSpecData) => {
    set((state) => {
      const newPlayerBalances: Record<string, number> = {};
      const newPlayerInsufficientBalance: Record<string, boolean> = {};

      // Extract balance information from players data
      Object.entries(data.players).forEach(([userId, player]) => {
        newPlayerBalances[userId] = player.balance;
        newPlayerInsufficientBalance[userId] = player.insufficientBalance;
      });

      return {
        realTimeRoomData: {
          ...state.realTimeRoomData,
          participantCount: data.participantCount,
          prizePool: data.prizePool,
          playerBalances: newPlayerBalances,
          playerInsufficientBalance: newPlayerInsufficientBalance,
          colorState: {
            playerColors: Object.fromEntries(
              Object.entries(data.players).map(([userId, player]) => [userId, player.color])
            ),
            availableColors: data.availableColors,
            takenColors: data.takenColors,
          },
        },
        // Update legacy state for backward compatibility
        playerColorSelections: Object.fromEntries(
          Object.entries(data.players).map(([userId, player]) => [userId, player.color])
        ),
        playerReadyStates: Object.fromEntries(
          Object.entries(data.players).map(([userId, player]) => [userId, player.ready])
        ),
      };
    });
  },

  handleBalanceUpdate: (data: BalanceUpdateData) => {
    set((state) => ({
      realTimeRoomData: {
        ...state.realTimeRoomData,
        playerBalances: {
          ...state.realTimeRoomData.playerBalances,
          [data.userId]: data.balance,
        },
      },
    }));
  },

  updateRealTimeRoomData: (data: Partial<GameState['realTimeRoomData']>) => {
    set((state) => ({
      realTimeRoomData: {
        ...state.realTimeRoomData,
        ...data,
        // Deep merge for nested objects
        playerBalances: {
          ...state.realTimeRoomData.playerBalances,
          ...(data.playerBalances || {}),
        },
        playerInsufficientBalance: {
          ...state.realTimeRoomData.playerInsufficientBalance,
          ...(data.playerInsufficientBalance || {}),
        },
        colorState: data.colorState ? {
          ...state.realTimeRoomData.colorState,
          ...data.colorState,
        } : state.realTimeRoomData.colorState,
      },
    }));
  },

  // Enhanced data getters
  getPlayerBalance: (playerId: string) => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.playerBalances[playerId];
  },

  getPlayerInsufficientBalance: (playerId: string) => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.playerInsufficientBalance[playerId] || false;
  },

  getRealTimeParticipantCount: () => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.participantCount;
  },

  getRealTimePrizePool: () => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.prizePool;
  },

  getRealTimeColorState: () => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.colorState;
  },

  // Amidakuji position management
  updatePlayerPositionSelection: (playerId: string, position: number) => {
    set((state) => ({
      playerPositionSelections: {
        ...state.playerPositionSelections,
        [playerId]: position,
      },
    }));
  },

  updateRealTimePositionState: (data: any) => {
    set((state) => ({
      realTimeRoomData: {
        ...state.realTimeRoomData,
        positionState: {
          ...state.realTimeRoomData.positionState,
          ...data,
        },
      },
      // Update legacy state for backward compatibility
      playerPositionSelections: {
        ...state.playerPositionSelections,
        ...(data.playerPositions || {}),
      },
    }));
  },

  getRealTimePositionState: () => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.positionState;
  },

  // Amidakuji game data management
  updateAmidakujiPattern: (data: any) => {
    set((state) => ({
      realTimeRoomData: {
        ...state.realTimeRoomData,
        amidakujiPattern: data,
      },
    }));
  },

  updateAmidakujiPathTracing: (data: any) => {
    set((state) => ({
      realTimeRoomData: {
        ...state.realTimeRoomData,
        amidakujiPathTracing: data,
      },
    }));
  },

  getAmidakujiPattern: () => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.amidakujiPattern;
  },

  getAmidakujiPathTracing: () => {
    const { realTimeRoomData } = get();
    return realTimeRoomData.amidakujiPathTracing;
  },
}));
