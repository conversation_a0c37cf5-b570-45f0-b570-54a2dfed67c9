import { create } from 'zustand';
import { socketService } from '@/services/socket';
import { useGameStore } from '@/store/gameStore';
import { useAuthStore } from '@/store/authStore';
import { useRoomSubscriptionStore } from '@/store/roomSubscriptionStore';
import { joinRoomManager } from '@/utils/joinRoomManager';
import { roomSubscriptionFlowManager } from '@/utils/roomSubscriptionFlow';
import { enhancedSubscriptionManager } from '@/utils/enhancedSubscriptionManager';
import { trackRoomInfoUpdate } from '@/utils/roomPlayerDiagnostics';
import { env } from '@/config/env';
import toast from 'react-hot-toast';
import type {
  ConnectionInfo,
  RoomState,
  NotificationData,
  GameStartingData,
  GameStartedData,
  GameUpdateData,
  GameFinishedData,
  PlayerJoinedData,
  PlayerLeftData,
  PlayerReadyStatusData,
  ColorStateUpdateData,
  RoomUpdateSpecData,
  RoomInfoUpdatedData,
  BalanceUpdateData,
  GameSpecificData,
} from '@/types/socket';
import { getColorById } from '@/constants/wheelColors';

// Event deduplication utility
const eventCache = new Map<string, { timestamp: number; dataHash: string }>();
const CACHE_EXPIRY_TIME = 5000; // 5 seconds

function shouldSkipDuplicateEvent(
  eventType: string,
  roomId: string,
  dataHash: string
): boolean {
  const cacheKey = `${eventType}_${roomId}`;
  const cached = eventCache.get(cacheKey);

  // Clean up expired entries
  if (cached && Date.now() - cached.timestamp > CACHE_EXPIRY_TIME) {
    eventCache.delete(cacheKey);
  }

  // Check for duplicate
  if (cached && cached.dataHash === dataHash) {
    return true; // Skip duplicate
  }

  // Store new event
  eventCache.set(cacheKey, { timestamp: Date.now(), dataHash });
  return false; // Process event
}

interface SocketState {
  // Connection state
  connectionInfo: ConnectionInfo;
  isConnected: boolean;

  // Room state
  currentRoom: RoomState | null;
  playerPosition?: number;
  playerBalance?: number;

  // Room info loading state
  waitingForRoomInfo: boolean;
  roomInfoTimeout: NodeJS.Timeout | null;

  // Enhanced game-specific data from room_info_updated events
  enhancedGameData: GameSpecificData | null;

  // Game state
  currentGame: {
    id: string;
    status: 'starting' | 'playing' | 'spinning' | 'results' | 'finished';
    startedAt?: Date;
    data?: any;
  } | null;

  // Notifications
  notifications: NotificationData[];

  // Actions
  connect: () => Promise<void>;
  disconnect: () => void;
  setWaitingForRoomInfo: (waiting: boolean, roomId?: string) => void;
  joinRoom: (roomId: string, password?: string, betAmount?: number) => Promise<void>;
  leaveRoom: (roomId: string, reason?: 'voluntary' | 'disconnected' | 'kicked') => Promise<void>;
  setPlayerReady: (roomId: string, isReady: boolean) => Promise<void>;
  selectWheelColor: (roomId: string, colorId: string) => Promise<void>;
  selectAmidakujiPosition: (roomId: string, position: number) => Promise<void>;
  isJoiningRoom: (roomId: string) => boolean;
  isLeavingRoom: (roomId: string) => boolean;
  clearNotifications: () => void;
  removeNotification: (id: string) => void;
}

export const useSocketStore = create<SocketState>((set) => ({
  // Initial state
  connectionInfo: {
    state: 'disconnected',
    reconnectAttempts: 0,
  },
  isConnected: false,
  currentRoom: null,
  waitingForRoomInfo: false,
  roomInfoTimeout: null,
  enhancedGameData: null,
  currentGame: null,
  notifications: [],

  // Actions
  connect: async () => {
    try {
      await socketService.connect();
    } catch (error) {
      console.error('Failed to connect to socket:', error);
      throw error;
    }
  },

  disconnect: () => {
    socketService.disconnect();
  },

  // Room info loading state management
  setWaitingForRoomInfo: (waiting: boolean, roomId?: string) => {
    set((state) => {
      // Clear existing timeout if any
      if (state.roomInfoTimeout) {
        clearTimeout(state.roomInfoTimeout);
      }

      let newTimeout: NodeJS.Timeout | null = null;

      // Set timeout to automatically clear waiting state after 10 seconds
      if (waiting && roomId) {
        newTimeout = setTimeout(() => {
          console.warn(`⚠️ Room info timeout for room ${roomId} - clearing waiting state`);
          set(() => ({
            waitingForRoomInfo: false,
            roomInfoTimeout: null,
          }));
        }, 10000); // 10 second timeout
      }

      return {
        waitingForRoomInfo: waiting,
        roomInfoTimeout: newTimeout,
      };
    });
  },

  joinRoom: async (roomId: string, password?: string, betAmount?: number) => {
    // Set waiting state before attempting to join
    const { setWaitingForRoomInfo } = useSocketStore.getState();
    setWaitingForRoomInfo(true, roomId);

    try {
      // Use the enhanced subscription manager for automatic lobby fallback
      const result = await enhancedSubscriptionManager.handleRoomJoinAttempt(roomId, async () => {
        return await socketService.joinRoom(roomId, password, betAmount);
      });

      if (result.success) {
        if (env.DEBUG) {
          console.log('Enhanced room join completed successfully:', {
            roomId,
            roomData: result.data,
            timestamp: new Date().toISOString()
          });
        }
        // Note: waitingForRoomInfo will be cleared when room_info_updated is received
      } else {
        console.error('Enhanced room join failed:', result.error);

        // Clear waiting state on failure
        setWaitingForRoomInfo(false);

        // Update socket store state to reflect failure
        set((state) => ({
          notifications: [...state.notifications, {
            id: `join-error-${Date.now()}`,
            type: 'error',
            title: 'Failed to Join Room',
            message: result.error?.message || 'Unknown error',
            timestamp: new Date().toISOString(),
          }]
        }));

        // Re-throw error for caller to handle
        throw result.error;
      }
    } catch (error) {
      // Clear waiting state on any error
      setWaitingForRoomInfo(false);
      throw error;
    }
  },

  // Legacy join room method for backward compatibility
  joinRoomLegacy: async (roomId: string, password?: string, betAmount?: number) => {
    // Use the global join room manager to prevent duplicate requests
    return joinRoomManager.joinRoom(roomId, async () => {
      const maxRetries = 2;
      let lastError: Error | null = null;

      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          await socketService.joinRoom(roomId, password, betAmount);

          // Success - the room_joined event will be handled by the event listener
          // which will update the current room state

          if (env.DEBUG) {
            console.log(`✅ Joined room: ${roomId}`);
          }

          return;
        } catch (error) {
          lastError = error as Error;
          const errorCode = (error as any).code;

          // Don't retry for certain error types
          const nonRetryableErrors = [
            'ROOM_FULL',
            'INSUFFICIENT_BALANCE',
            'INVALID_ROOM_PASSWORD',
            'PLAYER_ALREADY_IN_ROOM',
            'ROOM_NOT_FOUND',
            'AUTHENTICATION_FAILED'
          ];

          if (nonRetryableErrors.includes(errorCode)) {
            console.error('Non-retryable error, failing immediately:', error);
            throw error;
          }

          // For retryable errors, wait before retrying (except on last attempt)
          if (attempt < maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, attempt), 3000); // Exponential backoff, max 3s
            console.warn(`Join room attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // If we get here, all retries failed
      console.error('All join room attempts failed:', lastError);
      throw lastError || new Error('Failed to join room after multiple attempts');
    });
  },

  leaveRoom: async (roomId: string, reason: 'voluntary' | 'disconnected' | 'kicked' = 'voluntary') => {
    try {
      // Execute the leave room request
      await socketService.leaveRoom(roomId, reason);

      // Handle subscription management after successful leave
      await enhancedSubscriptionManager.handleRoomLeave(roomId, reason);

      if (env.DEBUG) {
        console.log('Enhanced leave room completed successfully:', {
          roomId,
          reason
        });
      }

      // The room_left event will handle additional state cleanup
    } catch (error: any) {
      console.error('Enhanced leave room failed:', {
        roomId,
        reason,
        error: error.message,
        code: error.code,
        details: error.details
      });

      // Add user-friendly error message based on error code
      let userMessage = error.message;
      if (error.code === 'PLAYER_NOT_IN_ROOM') {
        userMessage = 'You are not currently in this room.';
      } else if (error.code === 'ROOM_NOT_FOUND') {
        userMessage = 'The room no longer exists.';
      } else if (error.code === 'LEAVE_ROOM_TIMEOUT') {
        userMessage = 'Request timed out. Please try again.';
      } else if (error.code === 'UNAUTHORIZED') {
        userMessage = 'You are not authorized to leave this room.';
      }

      // Create enhanced error with user-friendly message
      const enhancedError = new Error(userMessage);
      (enhancedError as any).code = error.code;
      (enhancedError as any).originalError = error;

      throw enhancedError;
    }
  },

  setPlayerReady: async (roomId: string, isReady: boolean) => {
    try {
      await socketService.setPlayerReady(roomId, isReady);
    } catch (error) {
      console.error('Failed to set player ready:', error);
      throw error;
    }
  },

  selectWheelColor: async (roomId: string, colorId: string) => {
    try {
      await socketService.selectWheelColor(roomId, colorId);
    } catch (error) {
      console.error('Failed to select wheel color:', error);
      throw error;
    }
  },

  selectAmidakujiPosition: async (roomId: string, position: number) => {
    try {
      await socketService.selectAmidakujiPosition(roomId, position);
    } catch (error) {
      console.error('Failed to select Amidakuji position:', error);
      throw error;
    }
  },

  isJoiningRoom: (roomId: string) => {
    return joinRoomManager.isJoining(roomId);
  },

  isLeavingRoom: (roomId: string) => {
    return joinRoomManager.isLeaving(roomId);
  },

  clearNotifications: () => {
    set({ notifications: [] });
  },

  removeNotification: (id: string) => {
    set((state) => ({
      notifications: state.notifications.filter(n => n.id !== id),
    }));
  },
}));

// Set up socket event listeners
const setupSocketListeners = () => {
  // Connection events
  socketService.on('connectionStateChanged', (connectionInfo: ConnectionInfo) => {
    useSocketStore.setState({
      connectionInfo,
      isConnected: connectionInfo.state === 'connected',
    });
  });

  // Enhanced room events with comprehensive join handling
  socketService.on('room_joined', (data: any) => {
    const { room, player } = data;

    if (env.DEBUG) {
      console.log('Enhanced room joined event received:', { room, player, enhanced: data.enhanced });
    }

    // Clear any previous game state before joining new room
    useGameStore.getState().clearGameState();

    // Enhanced room state update with player position and balance
    useSocketStore.setState({
      currentRoom: room,
      currentGame: null, // Clear any previous game state
      playerPosition: player?.position,
      playerBalance: player?.balance,
    });

    // Update balance if provided
    if (player?.balance !== undefined) {
      useAuthStore.getState().updateBalance(player.balance);
    }

    // Automatically subscribe to room for real-time updates using comprehensive flow
    roomSubscriptionFlowManager.handlePostJoinRoomSubscription(room.id).catch((error: Error) => {
      console.error('SocketStore: Failed to handle post-join room subscription:', error);
      // Error handling is already done in the flow manager
    });

    // Enhanced success notification with position info
    const positionText = player?.position ? ` (Position ${player.position})` : '';
    const enhancedText = data.enhanced ? ' [Enhanced]' : '';
    toast.success(`Joined room: ${room.name}${positionText}${enhancedText}\nPlayers: ${room.playerCount}/${room.maxPlayers}`);

    console.log('Enhanced room joined successfully:', {
      roomId: room.id,
      roomName: room.name,
      playerPosition: player?.position,
      playerCount: room.playerCount,
      maxPlayers: room.maxPlayers,
      roomStatus: room.status,
      betAmount: room.betAmount,
      prizePool: room.prizePool,
      enhanced: data.enhanced || false,
      timestamp: data.timestamp
    });
  });

  socketService.on('room_left', (data: any) => {
    const { roomId, reason, previousRoom, autoLeave } = data;

    if (env.DEBUG) {
      console.log('Room left event received:', { roomId, reason, autoLeave });
    }

    // Clear current room state
    useSocketStore.setState({
      currentRoom: null,
      currentGame: null, // Clear any game state
    });

    // Clear all game-related state in game store
    useGameStore.getState().clearGameState();

    // Handle comprehensive subscription cleanup using flow manager
    roomSubscriptionFlowManager.handlePostLeaveSubscriptionCleanup(roomId, autoLeave || false).catch((error: Error) => {
      console.error('SocketStore: Failed to handle post-leave subscription cleanup:', error);
      // Error handling is already done in the flow manager
    });

    console.log('Successfully left room and updated state:', {
      roomId,
      reason,
      autoLeave: autoLeave || false,
      previousRoomName: previousRoom?.name
    });
  });

  // Enhanced leave room events
  socketService.on('reconnection_enabled', (data: any) => {
    const { roomId, reason, reconnectionWindow } = data;

    if (env.DEBUG) {
      console.log('Reconnection enabled event received:', { roomId, reason, reconnectionWindow });
    }

    // Don't clear room state - keep it for reconnection
    // Show reconnection notification to user
    useSocketStore.setState(state => ({
      notifications: [...state.notifications, {
        id: `reconnection-${Date.now()}`,
        type: 'info',
        title: 'Reconnection Available',
        message: `You can reconnect to the room within ${Math.floor(reconnectionWindow / 60)} minutes.`,
        timestamp: new Date().toISOString(),
        autoHide: false
      }]
    }));

    console.log('Reconnection enabled - room state preserved:', {
      roomId,
      reconnectionWindow
    });
  });

  socketService.on('player_ready_disconnected', (data: any) => {
    const { roomId, canReconnect, reconnectionWindow } = data;

    if (env.DEBUG) {
      console.log('Ready player disconnected event received:', { roomId, canReconnect, reconnectionWindow });
    }

    // Show reconnection notification
    useSocketStore.setState(state => ({
      notifications: [...state.notifications, {
        id: `ready-disconnect-${Date.now()}`,
        type: 'warning',
        title: 'Connection Lost',
        message: `You were disconnected while ready. Your spot is reserved for ${Math.floor(reconnectionWindow / 60)} minutes.`,
        timestamp: new Date().toISOString(),
        autoHide: false
      }]
    }));
  });

  socketService.on('reconnection_successful', (data: any) => {
    const { roomId, wasReady } = data;

    if (env.DEBUG) {
      console.log('Reconnection successful event received:', { roomId, wasReady });
    }

    // Show success notification
    useSocketStore.setState(state => ({
      notifications: [...state.notifications, {
        id: `reconnection-success-${Date.now()}`,
        type: 'success',
        title: 'Reconnected Successfully',
        message: `You have been reconnected to the room${wasReady ? ' and your ready status has been restored' : ''}.`,
        timestamp: new Date().toISOString(),
        autoHide: true
      }]
    }));
  });

  // Enhanced player events
  socketService.on('player_left_enhanced', (data: any) => {
    const { playerId, username, reason, source, roomState } = data;

    if (env.DEBUG) {
      console.log('Enhanced player left event received:', { playerId, username, reason, source });
    }

    // Update current room if we're in the same room
    const currentRoom = useSocketStore.getState().currentRoom;
    if (currentRoom && roomState) {
      useSocketStore.setState({
        currentRoom: {
          ...currentRoom,
          playerCount: roomState.playerCount || currentRoom.playerCount
        }
      });
    }
  });

  socketService.on('player_disconnected_ready', (data: any) => {
    const { playerId, username, canReconnect, reconnectionWindow } = data;

    if (env.DEBUG) {
      console.log('Player disconnected ready event received:', { playerId, username, canReconnect });
    }

    // Show notification about ready player disconnection
    useSocketStore.setState(state => ({
      notifications: [...state.notifications, {
        id: `player-disconnect-${playerId}-${Date.now()}`,
        type: 'info',
        title: 'Player Temporarily Disconnected',
        message: `${username} disconnected but can reconnect within ${Math.floor(reconnectionWindow / 60)} minutes.`,
        timestamp: new Date().toISOString(),
        autoHide: true
      }]
    }));
  });

  socketService.on('player_reconnected', (data: any) => {
    const { playerId, username } = data;

    if (env.DEBUG) {
      console.log('Player reconnected event received:', { playerId, username });
    }

    // Show notification about player reconnection
    useSocketStore.setState(state => ({
      notifications: [...state.notifications, {
        id: `player-reconnect-${playerId}-${Date.now()}`,
        type: 'success',
        title: 'Player Reconnected',
        message: `${username} has reconnected to the room.`,
        timestamp: new Date().toISOString(),
        autoHide: true
      }]
    }));
  });

  socketService.on('positions_updated', (data: any) => {
    const { roomId, positions } = data;

    if (env.DEBUG) {
      console.log('Positions updated event received:', { roomId, positionsCount: positions?.length });
    }

    // Update current room with new position information if we're in the same room
    const currentRoom = useSocketStore.getState().currentRoom;
    if (currentRoom && currentRoom.id === roomId && positions) {
      // Update player positions in room state
      const updatedPlayers = currentRoom.players?.map(player => {
        const positionUpdate = positions.find((p: any) => p.userId === player.userId);
        return positionUpdate ? { ...player, position: positionUpdate.position } : player;
      }) || [];

      useSocketStore.setState({
        currentRoom: {
          ...currentRoom,
          players: updatedPlayers
        }
      });
    }
  });

  // Enhanced player join events
  socketService.on('player_joined', (data: PlayerJoinedData) => {
    const { currentRoom } = useSocketStore.getState();
    if (currentRoom) {
      // Check if roomState exists before accessing its properties
      if (data.roomState) {
        useSocketStore.setState({
          currentRoom: {
            ...currentRoom,
            playerCount: data.roomState.playerCount,
            readyCount: data.roomState.readyCount || 0,
          },
        });
      } else {
        // Enhanced fallback: try to use player data if available
        let newPlayerCount = currentRoom.playerCount + 1;
        let updatedPlayers = currentRoom.players || [];

        // If we have player data in the event, try to add them to the players array
        if (data.player && data.player.userId) {
          const playerExists = updatedPlayers.some(p => p.userId === data.player.userId);
          if (!playerExists) {
            // Add the new player to the array with correct type structure
            updatedPlayers = [...updatedPlayers, {
              userId: data.player.userId,
              username: data.player.username || 'Unknown Player',
              avatar: data.player.avatar,
              position: updatedPlayers.length, // Assign next available position
              isReady: false,
              isHost: false,
              // Optional properties that might not be in the event
              balance: undefined,
              insufficientBalance: false,
            }];
            // Use the actual array length as player count
            newPlayerCount = updatedPlayers.length;
          } else {
            // Player already exists, don't increment
            newPlayerCount = currentRoom.playerCount;
          }
        }

        useSocketStore.setState({
          currentRoom: {
            ...currentRoom,
            playerCount: newPlayerCount,
            players: updatedPlayers,
          },
        });

        if (env.DEBUG) {
          console.warn('player_joined event missing roomState, using enhanced fallback:', {
            originalData: data,
            newPlayerCount,
            playersLength: updatedPlayers.length,
            addedPlayer: data.player?.username || 'Unknown'
          });
        }
      }
    }
  });

  socketService.on('player_joined_enhanced', (data: any) => {
    const { playerId, username, position, roomState } = data;

    if (env.DEBUG) {
      console.log('Enhanced player joined event received:', { playerId, username, position });
    }

    // Update current room with enhanced player join data
    const currentRoom = useSocketStore.getState().currentRoom;
    if (currentRoom && roomState) {
      useSocketStore.setState({
        currentRoom: {
          ...currentRoom,
          playerCount: roomState.playerCount || currentRoom.playerCount,
          readyCount: roomState.readyCount || currentRoom.readyCount,
          players: roomState.players || currentRoom.players
        }
      });

      // Show enhanced notification
      toast.success(`${username} joined the room (Position ${position})`);
    }
  });

  socketService.on('player_left', (data: PlayerLeftData) => {
    const { currentRoom } = useSocketStore.getState();
    if (currentRoom) {
      // Check if roomState exists before accessing its properties
      if (data.roomState) {
        useSocketStore.setState({
          currentRoom: {
            ...currentRoom,
            playerCount: data.roomState.playerCount,
            readyCount: data.roomState.readyCount || 0,
          },
        });
      } else {
        // Enhanced fallback: try to remove player from array if we have player ID
        let newPlayerCount = Math.max(0, currentRoom.playerCount - 1);
        let updatedPlayers = currentRoom.players || [];

        // If we have player ID in the event, try to remove them from the players array
        if (data.playerId) {
          const playerIndex = updatedPlayers.findIndex(p => p.userId === data.playerId);
          if (playerIndex !== -1) {
            // Remove the player from the array
            updatedPlayers = updatedPlayers.filter(p => p.userId !== data.playerId);
            // Use the actual array length as player count
            newPlayerCount = updatedPlayers.length;
          }
        }

        useSocketStore.setState({
          currentRoom: {
            ...currentRoom,
            playerCount: newPlayerCount,
            players: updatedPlayers,
          },
        });

        if (env.DEBUG) {
          console.warn('player_left event missing roomState, using enhanced fallback:', {
            originalData: data,
            newPlayerCount,
            playersLength: updatedPlayers.length,
            removedPlayer: data.username || data.playerId || 'Unknown'
          });
        }
      }
    }
  });

  socketService.on('player_ready_status', (data: PlayerReadyStatusData) => {
    const { currentRoom } = useSocketStore.getState();
    if (currentRoom) {
      useSocketStore.setState({
        currentRoom: {
          ...currentRoom,
          readyCount: data.readyCount,
          canStartGame: data.canStartGame || false,
        },
      });
    }

    // Update game store with player ready state
    useGameStore.getState().updatePlayerReadyState(data.playerId, data.isReady);

    if (env.DEBUG) {
      console.log('Player ready status updated:', {
        playerId: data.playerId,
        username: data.username,
        isReady: data.isReady,
        readyCount: data.readyCount,
        totalPlayers: data.totalPlayers,
        canStartGame: data.canStartGame,
      });
    }
  });

  // Additional event listener for player ready changes from game service
  socketService.on('player_ready_changed', (data: PlayerReadyStatusData) => {
    const { currentRoom } = useSocketStore.getState();
    if (currentRoom) {
      useSocketStore.setState({
        currentRoom: {
          ...currentRoom,
          readyCount: data.readyCount,
          canStartGame: data.canStartGame || false,
        },
      });
    }

    // Update game store with player ready state
    useGameStore.getState().updatePlayerReadyState(data.playerId, data.isReady);

    if (env.DEBUG) {
      console.log('Player ready changed (from game service):', {
        playerId: data.playerId,
        username: data.username,
        isReady: data.isReady,
        readyCount: data.readyCount,
        totalPlayers: data.totalPlayers,
        canStartGame: data.canStartGame,
      });
    }
  });

  // Note: Removed wheel_color_selected event handler to prevent duplicate processing
  // All color selection updates are now handled by room_color_state_updated event

  // Comprehensive room color state updates
  socketService.on('room_color_state_updated', (data: any) => {
    const gameStore = useGameStore.getState();

    // Update all player color selections
    if (data.playerColors) {
      Object.entries(data.playerColors).forEach(([playerId, playerInfo]: [string, any]) => {
        if (playerInfo.colorId) {
          gameStore.updatePlayerColorSelection(playerId, playerInfo.colorId);
        }

        // Update ready state if available
        if (typeof playerInfo.isReady === 'boolean') {
          gameStore.updatePlayerReadyState(playerId, playerInfo.isReady);
        }
      });
    }

    // Update comprehensive real-time color state
    if (data.availableColors || data.playerColors) {
      const takenColors: Record<string, boolean> = {};
      const playerColorMap: Record<string, string> = {};

      if (data.playerColors) {
        Object.entries(data.playerColors).forEach(([playerId, playerInfo]: [string, any]) => {
          if (playerInfo.colorId) {
            takenColors[playerInfo.colorId] = true;
            playerColorMap[playerId] = playerInfo.colorId;
          }
        });
      }

      gameStore.updateRealTimeRoomData({
        participantCount: data.totalPlayers,
        colorState: {
          playerColors: playerColorMap,
          availableColors: data.availableColors || [],
          takenColors,
        },
      });
    }

    // Show notification for other players' color selections
    const currentUser = useAuthStore.getState().user;
    if (currentUser && data.selectorUserId && data.selectorUserId !== currentUser.id && data.selectedColorId) {
      // Optional: Show subtle notification for other players' selections
      if (env.DEBUG) {
        console.log(`${data.selectorUsername} selected ${data.selectedColorId} color`);
      }
    }

    if (env.DEBUG) {
      console.log('Room color state updated:', {
        roomId: data.roomId,
        selectorUserId: data.selectorUserId,
        selectorUsername: data.selectorUsername,
        selectedColorId: data.selectedColorId,
        totalPlayers: data.totalPlayers,
        availableColorsCount: data.availableColors?.length || 0,
        playerColorsCount: Object.keys(data.playerColors || {}).length,
        timestamp: data.timestamp,
      });
    }
  });

  // Game events
  socketService.on('game_starting', (data: GameStartingData) => {
    useSocketStore.setState({
      currentGame: {
        id: data.gameId,
        status: 'starting',
        data,
      },
    });
  });

  socketService.on('game_started', (data: GameStartedData) => {
    useSocketStore.setState({
      currentGame: {
        id: data.gameId,
        status: 'playing',
        startedAt: new Date(data.startedAt),
        data,
      },
    });
  });

  socketService.on('game_update', (data: GameUpdateData) => {
    const { currentGame } = useSocketStore.getState();
    if (currentGame && currentGame.id === data.gameId) {
      useSocketStore.setState({
        currentGame: {
          ...currentGame,
          data: {
            ...currentGame.data,
            ...data.data,
          },
        },
      });
    }
  });

  socketService.on('game_finished', (data: GameFinishedData) => {
    useSocketStore.setState({
      currentGame: {
        id: data.gameId,
        status: 'finished',
        data,
      },
    });

    // Clear game state after a delay
    setTimeout(() => {
      const { currentGame } = useSocketStore.getState();
      if (currentGame && currentGame.id === data.gameId) {
        useSocketStore.setState({ currentGame: null });
      }
    }, 10000); // Clear after 10 seconds
  });

  // Prize Wheel specific events
  socketService.on('wheel_spinning', (data: any) => {
    const { currentGame } = useSocketStore.getState();
    if (currentGame && currentGame.id === data.gameId) {
      useSocketStore.setState({
        currentGame: {
          ...currentGame,
          status: 'spinning',
          data: {
            ...currentGame.data,
            animation: data.animation,
            participants: data.participants,
            isSpinning: true,
          },
        },
      });
    }

    if (env.DEBUG) {
      console.log('Wheel spinning event received:', {
        gameId: data.gameId,
        animation: data.animation,
        duration: data.animation?.duration,
        finalPosition: data.animation?.finalPosition,
        participants: data.participants,
      });
    }
  });

  socketService.on('wheel_stopped', (data: any) => {
    const { currentGame } = useSocketStore.getState();
    if (currentGame && currentGame.id === data.gameId) {
      useSocketStore.setState({
        currentGame: {
          ...currentGame,
          status: 'results',
          data: {
            ...currentGame.data,
            finalPosition: data.finalPosition,
            winnerPosition: data.winnerPosition,
            winningColor: data.winningColor,
            winner: data.winner,
            results: data.results,
            isSpinning: false,
          },
        },
      });

      // Show winner notification
      if (data.winner && data.winner.userId) {
        const notification: NotificationData = {
          id: `winner-${data.gameId}-${Date.now()}`,
          type: 'success',
          title: 'Game Results',
          message: `Winner: ${data.winner.userId} - Prize: ${data.winner.winAmount} coins`,
          timestamp: new Date().toISOString(),
        };

        useSocketStore.setState((state) => ({
          notifications: [...state.notifications, notification],
        }));
      }
    }

    if (env.DEBUG) {
      console.log('Wheel stopped event received:', {
        gameId: data.gameId,
        finalPosition: data.finalPosition,
        winningColor: data.winningColor,
        winner: data.winner,
        results: data.results,
      });
    }
  });

  socketService.on('countdown_update', (data: any) => {
    const { currentGame } = useSocketStore.getState();
    if (currentGame && currentGame.id === data.gameId) {
      useSocketStore.setState({
        currentGame: {
          ...currentGame,
          data: {
            ...currentGame.data,
            countdown: data.countdown,
          },
        },
      });
    }

    if (env.DEBUG) {
      console.log('Countdown update:', {
        gameId: data.gameId,
        countdown: data.countdown,
      });
    }
  });

  socketService.on('game_cancelled', () => {
    useSocketStore.setState({ currentGame: null });
  });

  // Balance update events
  socketService.on('balance_updated', (data: any) => {
    if (env.DEBUG) {
      console.log('Balance updated event received:', {
        gameId: data.gameId,
        prizePool: data.prizePool,
        winners: data.winners,
      });
    }

    // Show balance update notification
    const notification: NotificationData = {
      id: `balance-update-${data.gameId}-${Date.now()}`,
      type: 'info',
      title: 'Balance Updated',
      message: `Prize pool of ${data.prizePool} coins distributed to ${data.winners} winner(s)`,
      timestamp: new Date().toISOString(),
    };

    useSocketStore.setState((state) => ({
      notifications: [...state.notifications, notification],
    }));
  });

  // Game Service Sync Handler - handles periodic sync events from game service
  const handleGameServiceSync = (data: any) => {
    const roomId = data.roomId || (data.roomState as any)?.id;
    const timestamp = data.timestamp || new Date().toISOString();

    if (env.DEBUG) {
      console.log('🔄 Game Service Sync received:', {
        roomId,
        playerCount: data.roomState?.playerCount,
        playersLength: data.players?.length,
        source: data.source,
        timestamp,
        // Show conflict if current room has different player count
        currentRoomPlayerCount: useSocketStore.getState().currentRoom?.playerCount,
      });
    }

    // For game service sync, we only update if the data is more recent
    // and doesn't conflict with real-time data
    const { currentRoom } = useSocketStore.getState();

    if (currentRoom && currentRoom.id === roomId) {
      // Only update non-critical fields from game service sync
      // Avoid overriding real-time player data unless it's clearly more accurate
      const syncPlayerCount = data.roomState?.playerCount || data.players?.length || 0;
      const currentPlayerCount = currentRoom.playerCount;

      // If game service shows 0 players but we have players, it might be stale data
      if (syncPlayerCount === 0 && currentPlayerCount > 0) {
        if (env.DEBUG) {
          console.warn('⚠️ Game service sync shows 0 players, keeping current data:', {
            roomId,
            currentPlayerCount,
            syncPlayerCount,
          });
        }
        return; // Skip this sync update
      }

      // Update only safe fields from game service sync
      useSocketStore.setState({
        currentRoom: {
          ...currentRoom,
          // Only update these fields if they seem reasonable
          prizePool: data.roomState?.prizePool ?? currentRoom.prizePool,
          betAmount: data.roomState?.betAmount ?? currentRoom.betAmount,
          // Keep existing player data unless sync data is clearly better
          ...(syncPlayerCount >= currentPlayerCount && {
            playerCount: syncPlayerCount,
            players: data.players || currentRoom.players,
          }),
        },
      });

      if (env.DEBUG) {
        console.log('✅ Applied game service sync update:', {
          roomId,
          updatedPlayerCount: useSocketStore.getState().currentRoom?.playerCount,
        });
      }
    }
  };

  // Optimized room info updated event handler with source-aware routing
  socketService.on('room_info_updated', (data: RoomInfoUpdatedData | any) => {
    // Track room info event for diagnostics (helps debug player position issues)
    trackRoomInfoUpdate(data);

    // Route game service sync events to dedicated handler
    if ((data as any).source === 'game_service_sync') {
      handleGameServiceSync(data);
      return;
    }

    const { currentRoom } = useSocketStore.getState();

    // Helper function to convert lowercase to uppercase for UI compatibility
    const normalizeGameType = (gameType: string): 'PRIZEWHEEL' | 'AMIDAKUJI' => {
      const upperType = gameType.toUpperCase();
      if (upperType === 'GAME_TYPE_PRIZE_WHEEL') return 'PRIZEWHEEL';
      if (upperType === 'GAME_TYPE_AMIDAKUJI') return 'AMIDAKUJI';
      return upperType as 'PRIZEWHEEL' | 'AMIDAKUJI';
    };

    const normalizeStatus = (status: string): 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED' => {
      return status.toUpperCase() as 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED';
    };

    // Handle different data structures - new format vs legacy format
    let roomId: string;
    let roomData: any;
    let roomStateData: any;
    let playersData: any[];
    let gameSpecificData: any;
    let timestamp: string;

    // Check if this is the new comprehensive format with game/metadata/players/room sections
    if (data.game && data.metadata && data.players && data.room && data.roomId) {
      // New comprehensive format: structured sections
      roomId = data.roomId;
      timestamp = data.timestamp || new Date().toISOString();

      // Map new comprehensive format to expected structure
      roomData = {
        id: data.roomId,
        name: data.room.name,
        gameType: data.room.game_type,
        status: data.room.status,
        playerCount: data.room.current_players,
        maxPlayers: data.room.max_players,
        minPlayers: data.room.min_players,
        betAmount: data.room.bet_amount,
        isPrivate: data.room.is_private,
        autoStart: false, // Not provided in this format
        createdAt: undefined, // Not provided in this format
        lastActivity: undefined, // Not provided in this format
        canJoin: data.room.has_space,
        canStart: data.game.canStart
      };

      roomStateData = {
        playerCount: data.room.current_players,
        readyCount: data.players.list?.filter((p: any) => p.isReady).length || 0,
        canStartGame: data.game.canStart || false,
        prizePool: data.room.prize_pool || 0,
        gameInProgress: false, // Not provided in this format
        countdown: null // Not provided in this format
      };

      playersData = data.players.list || [];
      gameSpecificData = {
        gameType: data.game.type,
        allColorsSelected: data.game.allColorsSelected,
        colorMappings: data.players.colorMappings,
        playersWithColors: data.players.withColors
      };

      if (env.DEBUG) {
        console.log('🔄 Processing new comprehensive format room_info_updated:', {
          roomId,
          reason: data.reason,
          action: data.metadata.action,
          playerCount: roomStateData.playerCount,
          playersLength: playersData.length,
          canStart: data.game.canStart,
          allColorsSelected: data.game.allColorsSelected,
          playersWithColors: data.players.withColors,
          timestamp
        });
      }
    } else if (data.roomId && data.gameType && data.status && !data.room && !data.roomState) {
      // New direct format: all data at root level
      roomId = data.roomId;
      timestamp = data.timestamp || new Date().toISOString();

      // Map new direct format to expected structure
      roomData = {
        id: data.roomId,
        name: data.name,
        gameType: data.gameType,
        status: data.status,
        playerCount: data.currentPlayers,
        maxPlayers: data.maxPlayers,
        minPlayers: data.minPlayers,
        betAmount: parseFloat(data.betAmount || '0'),
        isPrivate: data.isPrivate || false,
        autoStart: data.autoStart || false,
        createdAt: data.createdAt,
        lastActivity: data.lastActivity,
        canJoin: data.canJoin,
        canStart: data.canStart
      };

      roomStateData = {
        playerCount: data.currentPlayers,
        readyCount: data.players?.filter((p: any) => {
          return p.isReady !== undefined ? p.isReady :
                 (p.status === 'PLAYER_STATUS_READY' || p.status === 'ready');
        }).length || 0,
        canStartGame: data.canStart || false,
        prizePool: 0, // Not provided in new format
        gameInProgress: false, // Not provided in new format
        countdown: null // Not provided in new format
      };

      playersData = data.players || [];
      gameSpecificData = null; // Not provided in new format

      if (env.DEBUG) {
        console.log('🔄 Processing new direct format room_info_updated:', {
          roomId,
          playerCount: roomStateData.playerCount,
          playersLength: playersData.length,
          canStart: data.canStart,
          canJoin: data.canJoin,
          timestamp
        });
      }
    } else if (data.data && data.type === 'room_info_updated') {
      // Legacy nested format: data is nested under 'data' property
      const nestedData = data.data;
      roomId = nestedData.roomId;
      timestamp = data.timestamp;

      // Map nested format to expected structure
      roomData = {
        id: nestedData.roomId,
        name: nestedData.name,
        gameType: nestedData.gameType,
        status: nestedData.status,
        playerCount: nestedData.currentPlayers,
        maxPlayers: nestedData.maxPlayers,
        betAmount: parseFloat(nestedData.betAmount || '0'),
        isPrivate: nestedData.isPrivate || false,
        createdAt: nestedData.createdAt,
        lastActivity: nestedData.lastActivity
      };

      roomStateData = {
        playerCount: nestedData.currentPlayers,
        readyCount: nestedData.players?.filter((p: any) => {
          return p.isReady !== undefined ? p.isReady :
                 (p.status === 'PLAYER_STATUS_READY' || p.status === 'ready');
        }).length || 0,
        canStartGame: nestedData.canStart || false,
        prizePool: 0, // Not provided in nested format
        gameInProgress: false, // Not provided in nested format
        countdown: null // Not provided in nested format
      };

      playersData = nestedData.players || [];
      gameSpecificData = null; // Not provided in nested format

      if (env.DEBUG) {
        console.log('🔄 Processing legacy nested format room_info_updated:', {
          roomId,
          reason: data.reason,
          playerCount: roomStateData.playerCount,
          playersLength: playersData.length,
          timestamp
        });
      }
    } else {
      // Legacy complex format: data structure with room/roomState/players separation
      roomId = (data.roomState as any)?.id || data.room?.id;
      timestamp = data.timestamp;
      roomData = data.room;
      roomStateData = data.roomState;
      playersData = data.players || [];
      gameSpecificData = data.gameSpecificData;

      if (env.DEBUG) {
        console.log('🔄 Processing legacy complex format room_info_updated:', {
          roomId,
          playerCount: roomStateData?.playerCount,
          playersLength: playersData.length,
          hasRoomData: !!roomData,
          hasRoomStateData: !!roomStateData,
          hasGameSpecificData: !!gameSpecificData,
          timestamp
        });
      }
    }

    // Create a hash of the essential data to detect duplicates
    const essentialData = {
      roomId,
      playerCount: roomStateData?.playerCount || playersData?.length,
      readyCount: roomStateData?.readyCount,
      prizePool: roomStateData?.prizePool,
      players: playersData?.map((p: any) => ({ userId: p.userId, isReady: p.isReady, position: p.position })),
      source: 'real_time'
    };
    const dataHash = JSON.stringify(essentialData);

    // Use utility function for deduplication
    if (shouldSkipDuplicateEvent('room_info_updated', roomId, dataHash)) {
      if (env.DEBUG) {
        console.log('🔄 Skipping duplicate room_info_updated event:', {
          roomId,
          source: 'real_time',
        });
      }
      return;
    }

    // Use the extracted game-specific data
    const isPrizeWheel = gameSpecificData?.gameType === 'prizewheel' || gameSpecificData?.gameType === 'prize_wheel';

    if (env.DEBUG) {
      console.log('✅ Processing real-time room_info_updated:', {
        roomId,
        playerCount: roomStateData?.playerCount || playersData?.length,
        hasGameSpecificData: !!gameSpecificData,
        timestamp,
      });
    }

    // Debug room ID matching
    if (env.DEBUG) {
      console.log('🔍 Room ID matching check:', {
        hasCurrentRoom: !!currentRoom,
        currentRoomId: currentRoom?.id,
        incomingRoomId: roomId,
        roomIdsMatch: currentRoom?.id === roomId,
        willUpdate: !currentRoom || currentRoom.id === roomId,
        incomingPlayersCount: playersData?.length,
      });
    }

    // Update socket store with comprehensive room data
    // More flexible room matching logic
    const roomSubscriptionStore = useRoomSubscriptionStore.getState();
    const shouldUpdate = !currentRoom ||
                        currentRoom.id === roomId ||
                        (currentRoom && !currentRoom.id && roomId) || // Handle case where currentRoom has no ID
                        roomSubscriptionStore.subscribedRoomId === roomId || // Check subscription state
                        roomSubscriptionStore.isSubscribed; // If we're subscribed to any room, update

    if (env.DEBUG) {
      console.log('🔍 Room update decision:', {
        shouldUpdate,
        hasCurrentRoom: !!currentRoom,
        currentRoomId: currentRoom?.id,
        incomingRoomId: roomId,
        roomIdsMatch: currentRoom?.id === roomId,
        subscribedRoomId: roomSubscriptionStore.subscribedRoomId,
        isSubscribed: roomSubscriptionStore.isSubscribed,
        incomingPlayersCount: playersData?.length,
        incomingPlayers: playersData?.map((p: any) => ({
          userId: p.userId,
          username: p.username,
          position: p.position,
          isReady: p.isReady,
          betAmount: p.betAmount
        })),
        // Additional debug info
        shouldUpdateConditions: {
          noCurrentRoom: !currentRoom,
          roomIdMatch: currentRoom?.id === roomId,
          currentRoomHasNoId: currentRoom && !currentRoom.id && roomId,
          subscriptionMatch: roomSubscriptionStore.subscribedRoomId === roomId,
          isSubscribedToAny: roomSubscriptionStore.isSubscribed
        }
      });
    }

    if (shouldUpdate) {
      const updatedRoom = {
        id: roomId,
        name: roomData?.name || currentRoom?.name || 'Unknown Room',
        gameType: normalizeGameType(roomData?.gameType || 'PRIZEWHEEL'),
        status: normalizeStatus(roomData?.status || 'WAITING'),
        playerCount: roomStateData?.playerCount || 0,
        maxPlayers: roomData?.maxPlayers || 8,
        readyCount: roomStateData?.readyCount || 0,
        canStartGame: roomStateData?.canStartGame || false,
        betAmount: roomData?.betAmount || 0,
        prizePool: roomStateData?.prizePool || 0,
        participantCount: roomStateData?.playerCount || 0,
        // Add countdown to room state for UI access
        countdown: roomStateData?.countdown,
        // Update players with comprehensive data
        players: playersData.map((player: any) => {
          // Get color information from wheel colors if colorId is provided
          const colorInfo = player.colorId ? getColorById(player.colorId) : null;

          // Handle new player status format and comprehensive format
          const isReady = player.isReady !== undefined ? player.isReady :
                         (player.status === 'PLAYER_STATUS_READY' || player.status === 'ready');

          return {
            userId: player.userId,
            username: player.username,
            avatar: undefined, // Not provided in this event
            position: player.position, // Keep 1-based position as received from server
            isReady: isReady,
            isHost: false, // Would need to be determined separately
            balance: player.balance,
            insufficientBalance: player.insufficientBalance,
            colorId: player.colorId,
            colorName: colorInfo?.name || player.colorId,
            colorHex: colorInfo?.hex || player.colorHex,
            // Store original status for debugging
            status: player.status,
            betAmount: player.betAmount,
            joinedAt: player.joinedAt,
          };
        }),
        // Add new fields from the updated event format
        minPlayers: roomData?.minPlayers || currentRoom?.minPlayers || 2,
        isPrivate: roomData?.isPrivate || false,
        autoStart: roomData?.autoStart || false,
        canJoin: roomData?.canJoin !== undefined ? roomData.canJoin : true,
        createdAt: roomData?.createdAt,
        lastActivity: roomData?.lastActivity,

        // Preserve existing room properties if updating
        ...(currentRoom && {
          minBet: currentRoom.minBet,
          maxBet: currentRoom.maxBet,
          currentGame: currentRoom.currentGame,
          colorState: currentRoom.colorState,
        }),
      };

      useSocketStore.setState({
        currentRoom: updatedRoom,
        enhancedGameData: gameSpecificData || null,
        // Clear waiting state since we received room info
        waitingForRoomInfo: false,
        roomInfoTimeout: null,
      });

      // Clear any existing timeout
      const currentState = useSocketStore.getState();
      if (currentState.roomInfoTimeout) {
        clearTimeout(currentState.roomInfoTimeout);
      }

      if (env.DEBUG) {
        console.log('✅ Socket store updated with room data:', {
          roomId: updatedRoom.id,
          playerCount: updatedRoom.playerCount,
          playersLength: updatedRoom.players?.length,
          players: updatedRoom.players?.map((p: any) => ({
            userId: p.userId,
            username: p.username,
            position: p.position,
            isReady: p.isReady,
          })),
          waitingForRoomInfoCleared: true,
        });

        // Additional verification - check what's actually in the store now
        setTimeout(() => {
          const currentState = useSocketStore.getState();
          console.log('🔍 Socket store state after update:', {
            hasCurrentRoom: !!currentState.currentRoom,
            currentRoomId: currentState.currentRoom?.id,
            currentRoomPlayerCount: currentState.currentRoom?.playerCount,
            currentRoomPlayersLength: currentState.currentRoom?.players?.length,
            currentRoomPlayers: currentState.currentRoom?.players?.map((p: any) => ({
              userId: p.userId,
              username: p.username,
              position: p.position,
              isReady: p.isReady,
            })),
          });
        }, 100);
      }
    } else {
      if (env.DEBUG) {
        console.log('❌ Room update skipped - room ID mismatch or no current room');
      }
    }

    // Update game store with comprehensive room data
    const gameStore = useGameStore.getState();
    gameStore.updateRealTimeRoomData({
      participantCount: roomStateData?.playerCount || 0,
      prizePool: roomStateData?.prizePool || 0,
      playerBalances: Object.fromEntries(
        playersData
          .filter((p: any) => p.balance !== undefined)
          .map((p: any) => [p.userId, p.balance!])
      ),
      playerInsufficientBalance: Object.fromEntries(
        playersData.map((p: any) => [p.userId, p.insufficientBalance || false])
      ),
      colorState: (() => {
        // Use enhanced game-specific data if available for Prize Wheel
        if (isPrizeWheel && gameSpecificData && 'colorSelections' in gameSpecificData) {
          const prizeWheelData = gameSpecificData as import('../types/socket').PrizeWheelGameData;
          return {
            playerColors: prizeWheelData.colorSelections,
            availableColors: prizeWheelData.availableColors,
            takenColors: Object.fromEntries(
              Object.values(prizeWheelData.colorSelections).map(colorId => [colorId, true])
            ),
          };
        }

        // Fallback to extracting from player data
        return {
          playerColors: Object.fromEntries(
            playersData
              .filter((p: any) => p.colorId)
              .map((p: any) => [p.userId, p.colorId!])
          ),
          availableColors: [], // Would need to be calculated or provided
          takenColors: Object.fromEntries(
            playersData
              .filter((p: any) => p.colorId)
              .map((p: any) => [p.colorId!, true])
          ),
        };
      })(),
    });

    // Update player ready states and color selections
    playersData.forEach((player: any) => {
      // Handle new player status format
      const isReady = player.isReady !== undefined ? player.isReady :
                     (player.status === 'PLAYER_STATUS_READY' || player.status === 'ready');

      gameStore.updatePlayerReadyState(player.userId, isReady);
      if (player.colorId) {
        gameStore.updatePlayerColorSelection(player.userId, player.colorId);
      }
    });

    // Update enhanced color selections from game-specific data if available
    if (isPrizeWheel && gameSpecificData && 'colorSelections' in gameSpecificData) {
      const prizeWheelData = gameSpecificData as import('../types/socket').PrizeWheelGameData;

      // Update color selections with enhanced data
      Object.entries(prizeWheelData.colorSelections).forEach(([userId, colorId]) => {
        gameStore.updatePlayerColorSelection(userId, colorId);
      });

      // Update available colors in game store using enhanced data
      gameStore.handleColorStateUpdate({
        roomId: roomId,
        playerColors: prizeWheelData.colorSelections,
        availableColors: prizeWheelData.availableColors,
        takenColors: Object.fromEntries(
          Object.values(prizeWheelData.colorSelections).map(colorId => [colorId, true])
        ),
        timestamp: data.timestamp,
      });
    }

    console.log('Enhanced room info updated received:', {
      roomId: roomId,
      playerCount: roomStateData?.playerCount,
      readyCount: roomStateData?.readyCount,
      prizePool: roomStateData?.prizePool,
      gameInProgress: roomStateData?.gameInProgress,
      countdown: roomStateData?.countdown,
      playersCount: playersData?.length,
      players: playersData,
      hasCurrentRoom: !!currentRoom,
      currentRoomId: currentRoom?.id,
      roomMatches: currentRoom?.id === roomId,
      // Enhanced game-specific data logging
      hasGameSpecificData: !!gameSpecificData,
      gameSpecificDataType: gameSpecificData?.gameType,
      gameSpecificDataKeys: gameSpecificData ? Object.keys(gameSpecificData) : [],
      ...(isPrizeWheel && gameSpecificData && 'colorSelections' in gameSpecificData && {
        prizeWheelData: {
          colorSelectionsCount: Object.keys(gameSpecificData.colorSelections).length,
          availableColorsCount: gameSpecificData.availableColors.length,
          playerColorMappingsCount: Object.keys(gameSpecificData.playerColorMappings || {}).length,
          colorSelections: gameSpecificData.colorSelections,
          availableColors: gameSpecificData.availableColors,
        },
      }),
    });

    // Dispatch custom event for components using useRoomSubscription hook
    // This ensures player slot components receive the room_info_updated data
    // Create a normalized data structure for the handler
    const normalizedData = {
      room: roomData,
      roomState: roomStateData,
      players: playersData,
      gameSpecificData: gameSpecificData,
      timestamp: timestamp
    };
    roomSubscriptionFlowManager.handleRoomInfoUpdate(roomId, normalizedData);
  });

  // Enhanced real-time data events
  socketService.on('colorStateUpdate', (data: ColorStateUpdateData) => {
    // Update game store with enhanced color state
    useGameStore.getState().handleColorStateUpdate(data);

    if (env.DEBUG) {
      console.log('Color state update received:', {
        roomId: data.roomId,
        playerColorCount: Object.keys(data.playerColors).length,
        availableColorCount: data.availableColors.length,
        takenColorCount: Object.keys(data.takenColors).length,
        timestamp: data.timestamp,
      });
    }
  });

  socketService.on('room_update_spec', (data: RoomUpdateSpecData) => {
    // Update game store with real room data
    useGameStore.getState().handleRoomUpdateSpec(data);

    // Update socket store room state with real data
    const { currentRoom } = useSocketStore.getState();
    if (currentRoom && currentRoom.id === data.roomId) {
      useSocketStore.setState({
        currentRoom: {
          ...currentRoom,
          participantCount: data.participantCount,
          prizePool: data.prizePool,
          status: data.gameState as any,
          // Update players with real balance data
          players: currentRoom.players.map(player => {
            const realPlayerData = data.players[player.userId];
            if (realPlayerData) {
              return {
                ...player,
                balance: realPlayerData.balance,
                insufficientBalance: realPlayerData.insufficientBalance,
                colorId: realPlayerData.color,
                isReady: realPlayerData.ready,
              };
            }
            return player;
          }),
          // Update color state
          colorState: {
            playerColors: Object.fromEntries(
              Object.entries(data.players).map(([userId, player]) => [userId, player.color])
            ),
            availableColors: data.availableColors,
            takenColors: data.takenColors,
          },
        },
      });
    }

    if (env.DEBUG) {
      console.log('Room update spec received:', {
        roomId: data.roomId,
        gameState: data.gameState,
        participantCount: data.participantCount,
        prizePool: data.prizePool,
        playerCount: Object.keys(data.players).length,
        timestamp: data.timestamp,
      });
    }
  });

  // Room state updated event - comprehensive room state synchronization
  socketService.on('room_state_updated', (data: any) => {
    if (env.DEBUG) {
      console.log('Raw room_state_updated event data in socket store:', data);
    }

    const roomId = data.roomId;
    const roomState = data.roomState;
    const players = data.players;
    const gameConfig = data.gameConfig;
    const playerColors = data.playerColors;
    const timestamp = data.timestamp;

    if (env.DEBUG) {
      console.log('Room state updated event received in socket store:', {
        roomId,
        timestamp,
        playerCount: roomState?.playerCount,
        playersLength: players?.length,
        readyCount: roomState?.readyCount,
        canStartGame: roomState?.canStartGame,
        prizePool: roomState?.prizePool,
        gameConfig,
        playerColorsCount: Object.keys(playerColors || {}).length,
        hasPlayers: !!players,
        playersType: typeof players,
        allDataKeys: Object.keys(data),
      });
    }

    // Update current room state if we're in this room
    const { currentRoom } = useSocketStore.getState();
    if (currentRoom && currentRoom.id === roomId) {
      const updatedRoom = {
        ...currentRoom,
        // Update from roomState object
        ...(roomState || {}),
        // Ensure critical properties are properly mapped
        id: roomId, // Keep the original room ID
        // Update players array from root level (this is the key fix!)
        players: players || currentRoom.players || [],
        playerCount: roomState?.playerCount || players?.length || 0,
        readyCount: roomState?.readyCount || 0,
        canStartGame: roomState?.canStartGame || false,
        prizePool: roomState?.prizePool || 0,
        // Update from gameConfig if available
        betAmount: gameConfig?.betAmount || currentRoom.betAmount,
        maxPlayers: gameConfig?.maxPlayers || currentRoom.maxPlayers,
        gameType: gameConfig?.gameType || currentRoom.gameType,
        name: currentRoom.name, // Keep existing name
        status: currentRoom.status, // Keep existing status unless specified
      };

      useSocketStore.setState({
        currentRoom: updatedRoom,
      });

      if (env.DEBUG) {
        console.log('Updated current room state from room_state_updated:', {
          roomId: updatedRoom.id,
          playerCount: updatedRoom.playerCount,
          playersLength: updatedRoom.players?.length,
          readyCount: updatedRoom.readyCount,
          canStartGame: updatedRoom.canStartGame,
          prizePool: updatedRoom.prizePool,
          players: updatedRoom.players?.map((p: any) => ({
            userId: p.userId,
            username: p.username,
            position: p.position,
            isReady: p.isReady,
            betAmount: p.betAmount,
          })),
        });
      }

      // Also update the game store with real-time data
      const gameStore = useGameStore.getState();
      if (players) {
        // Update player ready states
        players.forEach((player: any) => {
          gameStore.updatePlayerReadyState(player.userId, player.isReady);
        });
      }

      // Update real-time room data in game store
      gameStore.updateRealTimeRoomData({
        participantCount: players?.length || roomState?.playerCount,
        prizePool: roomState?.prizePool,
        colorState: playerColors ? {
          playerColors: playerColors,
          availableColors: gameStore.getRealTimeColorState()?.availableColors || [],
          takenColors: Object.fromEntries(
            Object.entries(playerColors).map(([, colorId]) => [colorId as string, true])
          ),
        } : undefined,
      });
    }
  });

  socketService.on('balance_updated', (data: BalanceUpdateData) => {
    // Update game store with balance data
    useGameStore.getState().handleBalanceUpdate(data);

    if (env.DEBUG) {
      console.log('Balance updated:', {
        userId: data.userId,
        username: data.username,
        balance: data.balance,
        previousBalance: data.previousBalance,
        transactionType: data.transaction.type,
        transactionAmount: data.transaction.amount,
        timestamp: data.timestamp,
      });
    }
  });

  // Amidakuji position events
  socketService.on('amidakuji_position_selected', (data: any) => {
    // Update game store with position selection
    useGameStore.getState().updatePlayerPositionSelection(data.playerId, data.position);

    if (env.DEBUG) {
      console.log('Amidakuji position selected:', {
        playerId: data.playerId,
        username: data.username,
        position: data.position,
        timestamp: data.timestamp,
      });
    }
  });

  socketService.on('room_position_state_updated', (data: any) => {
    // Update game store with position state
    useGameStore.getState().updateRealTimePositionState(data);

    if (env.DEBUG) {
      console.log('Room position state updated:', {
        roomId: data.roomId,
        playerPositionCount: Object.keys(data.playerPositions || {}).length,
        availablePositionCount: (data.availablePositions || []).length,
        takenPositionCount: Object.keys(data.takenPositions || {}).length,
        timestamp: data.timestamp,
      });
    }
  });

  socketService.on('amidakuji_pattern', (data: any) => {
    // Update game store with pattern data
    useGameStore.getState().updateAmidakujiPattern(data);

    if (env.DEBUG) {
      console.log('Amidakuji pattern generated:', {
        gameId: data.gameId,
        columns: data.pattern?.columns,
        rows: data.pattern?.rows,
        participantCount: data.participants?.length,
      });
    }
  });

  socketService.on('path_tracing', (data: any) => {
    // Update game store with path tracing data
    useGameStore.getState().updateAmidakujiPathTracing(data);

    if (env.DEBUG) {
      console.log('Amidakuji path tracing:', {
        gameId: data.gameId,
        currentStep: data.tracing?.currentStep,
        totalSteps: data.tracing?.totalSteps,
        pathCount: data.tracing?.paths?.length,
      });
    }
  });

  // Notification events
  socketService.on('notification', (data: NotificationData) => {
    useSocketStore.setState((state) => ({
      notifications: [...state.notifications, data],
    }));

    // Auto-remove non-persistent notifications after 5 seconds
    if (!data.persistent) {
      setTimeout(() => {
        useSocketStore.getState().removeNotification(data.id);
      }, 5000);
    }
  });

  // Error events
  socketService.on('error', (data: any) => {
    const notification: NotificationData = {
      id: `error-${Date.now()}`,
      type: 'error',
      title: 'Socket Error',
      message: data.message || 'An error occurred',
      timestamp: new Date().toISOString(),
    };

    useSocketStore.setState((state) => ({
      notifications: [...state.notifications, notification],
    }));
  });

  // Enhanced room info update events (removed duplicate handler)

  // Room join failure events for enhanced subscription management
  socketService.on('room_join_failed', (data: any) => {
    if (env.DEBUG) {
      console.log('Room join failed event received:', data);
    }

    // The enhanced room subscription manager will handle lobby re-subscription
    // This event is mainly for additional error handling if needed
    const notification: NotificationData = {
      id: `join-failed-${Date.now()}`,
      type: 'error',
      title: 'Failed to Join Room',
      message: data.message || 'Unable to join room',
      timestamp: new Date().toISOString(),
    };

    useSocketStore.setState((state) => ({
      notifications: [...state.notifications, notification],
    }));
  });

  // Dedicated Game Service Sync Event Handler
  socketService.on('game_service_sync', (data: any) => {
    if (env.DEBUG) {
      console.log('🔄 Dedicated game_service_sync event received:', {
        roomId: data.roomId,
        playerCount: data.roomState?.playerCount,
        source: 'game_service_sync',
      });
    }

    // Use the same handler as routed game service sync events
    handleGameServiceSync({ ...data, source: 'game_service_sync' });
  });

  // Lobby update events for fresh room listings
  socketService.on('lobby_updated', (data: any) => {
    if (env.DEBUG) {
      console.log('Lobby updated event received:', data);
    }

    // Emit custom event for lobby components to handle
    window.dispatchEvent(new CustomEvent('lobbyUpdate', { detail: data }));
  });
};

// Initialize socket listeners
setupSocketListeners();

// Export store and utilities
export { setupSocketListeners };
