import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { config, env } from '@/config/env';
import type {
  ApiResponse,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
  Room,
  CreateRoomRequest,
  GetRoomsRequest,
  RoomsResponse,
  Transaction,
  PaginationParams,
} from '@/types/api';

class ApiClient {
  private client: AxiosInstance;
  private refreshPromise: Promise<string> | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: config.api.baseURL,
      timeout: config.timeouts.apiRequest,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add device ID for tracking
        const deviceId = this.getDeviceId();
        if (deviceId) {
          config.headers['X-Device-ID'] = deviceId;
        }

        if (env.DEBUG) {
          console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        if (env.DEBUG) {
          console.log('API Response:', response.status, response.config.url, response.data);
        }
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Don't try to refresh tokens for auth endpoints (login, register, refresh)
        const isAuthEndpoint = originalRequest.url?.includes('/auth/') || false;

        if (error.response?.status === 401 && !originalRequest._retry && !isAuthEndpoint) {
          originalRequest._retry = true;

          try {
            const newToken = await this.refreshAccessToken();
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            // Refresh failed, clear tokens and let the app handle redirect
            this.clearTokens();
            // Don't use window.location.href as it causes page reload
            // Let the app's auth state management handle the redirect
            return Promise.reject(refreshError);
          }
        }

        if (env.DEBUG) {
          console.error('API Error:', error.response?.status, error.config?.url, error.response?.data);
        }

        return Promise.reject(error);
      }
    );
  }

  private getAccessToken(): string | null {
    return localStorage.getItem(config.storage.accessToken);
  }

  private getRefreshToken(): string | null {
    return localStorage.getItem(config.storage.refreshToken);
  }

  private setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(config.storage.accessToken, accessToken);
    localStorage.setItem(config.storage.refreshToken, refreshToken);
  }

  private clearTokens(): void {
    localStorage.removeItem(config.storage.accessToken);
    localStorage.removeItem(config.storage.refreshToken);
    localStorage.removeItem(config.storage.user);
  }

  private getDeviceId(): string {
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = crypto.randomUUID();
      localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  }

  private async refreshAccessToken(): Promise<string> {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    this.refreshPromise = this.performTokenRefresh(refreshToken);

    try {
      const newToken = await this.refreshPromise;
      return newToken;
    } finally {
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(refreshToken: string): Promise<string> {
    const response = await axios.post<ApiResponse<{ token: string; refreshToken: string }>>(
      `${config.api.baseURL}${config.api.auth.refresh}`,
      { refreshToken },
      { timeout: config.timeouts.apiRequest }
    );

    if (!response.data.success || !response.data.data) {
      throw new Error('Token refresh failed');
    }

    const { token, refreshToken: newRefreshToken } = response.data.data;
    this.setTokens(token, newRefreshToken);
    return token;
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      console.log('API: Attempting login with credentials:', { username: credentials.username });
      const response = await this.client.post<ApiResponse<AuthResponse>>(
        config.api.auth.login,
        credentials  // Send credentials directly for API Gateway
      );

      console.log('API: Login response received:', response.status, response.data);

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error?.message || 'Login failed');
      }

      const authData = response.data.data;
      this.setTokens(authData.token, authData.refreshToken);
      localStorage.setItem(config.storage.user, JSON.stringify(authData.user));

      console.log('API: Login successful, tokens stored');
      return authData;
    } catch (error: any) {
      console.error('API: Login error:', error);
      if (error.response) {
        console.error('API: Error response:', error.response.status, error.response.data);
      }
      throw error;
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      console.log('API: Attempting registration with data:', { username: userData.username, email: userData.email });
      const response = await this.client.post<ApiResponse<AuthResponse>>(
        config.api.auth.register,
        userData  // Send userData directly for API Gateway
      );

      console.log('API: Registration response received:', response.status, response.data);

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error?.message || 'Registration failed');
      }

      const authData = response.data.data;
      this.setTokens(authData.token, authData.refreshToken);
      localStorage.setItem(config.storage.user, JSON.stringify(authData.user));

      console.log('API: Registration successful, tokens stored');
      return authData;
    } catch (error: any) {
      console.error('API: Registration error:', error);
      if (error.response) {
        console.error('API: Error response:', error.response.status, error.response.data);
      }
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.client.post(config.api.auth.logout);
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  // User methods
  async getProfile(): Promise<User> {
    const response = await this.client.get<ApiResponse<User>>(config.api.users.profile);

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get profile');
    }

    return response.data.data;
  }

  async getBalance(): Promise<{ balance: number; currency: string }> {
    const response = await this.client.get<ApiResponse<{ balance: number; currency: string }>>(
      config.api.users.balance
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get balance');
    }

    return response.data.data;
  }

  async getTransactions(params: PaginationParams = {}): Promise<{ transactions: Transaction[]; pagination: any }> {
    const response = await this.client.get<ApiResponse<{ transactions: Transaction[]; pagination: any }>>(
      config.api.users.transactions,
      { params }
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get transactions');
    }

    return response.data.data;
  }

  // Helper method to transform backend room data to frontend format
  private transformRoomData(backendRoom: any): Room {
    return {
      id: backendRoom.id,
      name: backendRoom.name,
      gameType: backendRoom.game_type || backendRoom.gameType,
      status: backendRoom.status,
      creatorId: backendRoom.creator_id || backendRoom.creatorId,
      // Flat structure from backend
      maxPlayers: backendRoom.max_players || backendRoom.maxPlayers,
      currentPlayerCount: backendRoom.current_players || backendRoom.currentPlayerCount || backendRoom.player_count || 0,
      betAmount: backendRoom.bet_amount || backendRoom.betAmount,
      currency: backendRoom.currency || 'USD',
      gameDuration: backendRoom.game_duration || backendRoom.gameDuration,
      isPrivate: backendRoom.is_private || backendRoom.isPrivate || false,
      password: backendRoom.password,
      players: backendRoom.players || [],
      createdAt: backendRoom.created_at || backendRoom.createdAt,
      updatedAt: backendRoom.updated_at || backendRoom.updatedAt,
      // Create nested config for backward compatibility
      config: {
        maxPlayers: backendRoom.max_players || backendRoom.maxPlayers,
        betAmount: backendRoom.bet_amount || backendRoom.betAmount,
        currency: backendRoom.currency || 'USD',
        gameDuration: backendRoom.game_duration || backendRoom.gameDuration || 1800000,
        gameSpecificConfig: backendRoom.game_specific_config || backendRoom.gameSpecificConfig || {},
      },
    };
  }

  // Room methods
  async getRooms(params: GetRoomsRequest = {}): Promise<RoomsResponse> {
    const response = await this.client.get<ApiResponse<any>>(
      config.api.rooms.list,
      { params }
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get rooms');
    }

    // Transform the rooms data
    const transformedRooms = response.data.data.rooms?.map((room: any) => this.transformRoomData(room)) || [];

    return {
      rooms: transformedRooms,
      pagination: response.data.data.pagination,
    };
  }

  async createRoom(roomData: CreateRoomRequest): Promise<Room> {
    const response = await this.client.post<ApiResponse<{ room: any }>>(
      config.api.rooms.create,
      roomData
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to create room');
    }

    return this.transformRoomData(response.data.data.room);
  }

  async getRoomDetails(roomId: string): Promise<Room> {
    const response = await this.client.get<ApiResponse<{ room: any }>>(
      config.api.rooms.details(roomId)
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to get room details');
    }

    return this.transformRoomData(response.data.data.room);
  }

  async joinRoom(roomId: string, password?: string): Promise<{ room: Room; player: any }> {
    const response = await this.client.post<ApiResponse<{ room: any; player: any }>>(
      config.api.rooms.join(roomId),
      { password }
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error?.message || 'Failed to join room');
    }

    return {
      room: this.transformRoomData(response.data.data.room),
      player: response.data.data.player,
    };
  }

  async leaveRoom(roomId: string): Promise<void> {
    const response = await this.client.post<ApiResponse>(config.api.rooms.leave(roomId));

    if (!response.data.success) {
      throw new Error(response.data.error?.message || 'Failed to leave room');
    }
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem(config.storage.user);
    if (!userStr) return null;

    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  }

  getAuthToken(): string | null {
    return this.getAccessToken();
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
