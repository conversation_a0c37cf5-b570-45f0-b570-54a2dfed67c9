import React, { useState, useEffect, useMemo } from 'react';
import { Target, Clock, Trophy } from 'lucide-react';
import { PrizeWheel, ReadyPlayer } from './PrizeWheel';
import { ColorSelector } from './ColorSelector';
import { useSocketStore } from '../../store/socketStore';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { usePrizeWheelRealTimeData } from '../../hooks/useRealTimeGameData';
import { useEnhancedRoomData } from '../../hooks/useEnhancedRoomData';
import { useEnhancedColorSelection } from '../../hooks/useEnhancedColorSelection';
import { getColorHexById } from '../../constants/wheelColors';
import { WheelColor } from '../../types/socket';
import { ColorSelectionStats } from './ColorSelectionStats';
import { PlayerColorAssignments } from './PlayerColorAssignments';
import { ColorSelectionActivity } from './ColorSelectionActivity';
import toast from 'react-hot-toast';

interface PrizeWheelReadySectionProps {
  roomId: string;
  currentUserId: string;
  className?: string;
}

export const PrizeWheelReadySection: React.FC<PrizeWheelReadySectionProps> = ({
  roomId,
  currentUserId,
  className = '',
}) => {
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [isSpinning, setIsSpinning] = useState(false);
  const [winnerColor, setWinnerColor] = useState<string>('');
  const [gamePhase, setGamePhase] = useState<'waiting' | 'countdown' | 'spinning' | 'results'>('waiting');
  const [countdown, setCountdown] = useState<number>(0);
  const [isSelectingColor, setIsSelectingColor] = useState(false);
  const [isTogglingReady, setIsTogglingReady] = useState(false);

  const { setPlayerReady, selectWheelColor, currentRoom, currentGame } = useSocketStore();
  const { playerColorSelections, playerReadyStates } = useGameStore();
  const { user } = useAuthStore();

  // Get current user's ready state from the store instead of local state
  const isReady = playerReadyStates[currentUserId] || false;

  // Use the real-time data hook for Prize Wheel (legacy support)
  const {
    getAvailableColors,
    getTakenColors,
    getPlayerColors,
    getPlayerBalance,
    getPlayerInsufficientBalance,
  } = usePrizeWheelRealTimeData();

  // Use the enhanced room data hook for game-specific data
  const {
    getEnhancedColorData,
    hasEnhancedData,
    prizeWheelData,
  } = useEnhancedRoomData();

  // Use enhanced color selection hook for real-time color updates
  const {
    availableColors: enhancedAvailableColors,
    takenColors: enhancedTakenColors,
    playerColorMappings: enhancedPlayerColorMappings,
    statistics: colorStatistics,
    isLoading: isColorSelectionLoading,
    selectColor: enhancedSelectColor,
    currentUserColor: enhancedCurrentUserColor,
    hasSelectedColor: enhancedHasSelectedColor,
    lastUpdate: colorLastUpdate,
    // New comprehensive data from color_selection_updated event
    assignments: colorAssignments,
    selectedColors: selectedColorsData,
    currentPlayerColor: currentPlayerColorData,
  } = useEnhancedColorSelection();

  // Create available colors based on enhanced data or fallback to legacy
  const availableColors: WheelColor[] = useMemo(() => {
    // Always create the full color list
    const allColors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'teal'];

    // Check if we have enhanced color selection data (highest priority)
    if (enhancedAvailableColors.length > 0 || Object.keys(enhancedTakenColors).length > 0) {
      console.log('Using enhanced color selection data:', {
        availableCount: enhancedAvailableColors.length,
        takenCount: Object.keys(enhancedTakenColors).length,
        lastUpdate: colorLastUpdate
      });

      return allColors.map(colorId => ({
        id: colorId,
        name: colorId.charAt(0).toUpperCase() + colorId.slice(1),
        hex: getColorHexById(colorId),
        isAvailable: enhancedAvailableColors.some(color => color.id === colorId),
        selectedBy: enhancedTakenColors[colorId]?.userId,
      }));
    }

    // Get enhanced room data if available (second priority)
    const enhancedColorData = getEnhancedColorData();

    if (hasEnhancedData && enhancedColorData.source === 'enhanced') {
      // Use enhanced game-specific data
      console.log('Using enhanced room data:', enhancedColorData);

      return allColors.map(colorId => ({
        id: colorId,
        name: colorId.charAt(0).toUpperCase() + colorId.slice(1),
        hex: getColorHexById(colorId),
        isAvailable: enhancedColorData.availableColors.includes(colorId),
        selectedBy: Object.entries(enhancedColorData.playerColors)
          .find(([_, color]) => color === colorId)?.[0],
      }));
    }

    // Fallback to legacy real-time data
    const takenColors = getTakenColors();
    const realTimePlayerColors = getPlayerColors();

    // Combine real-time and legacy data for taken colors
    const combinedTakenColors = { ...takenColors };
    const combinedPlayerColors = { ...realTimePlayerColors };

    // Add legacy data to the mix
    Object.entries(playerColorSelections).forEach(([playerId, colorId]) => {
      if (colorId && !combinedPlayerColors[playerId]) {
        combinedPlayerColors[playerId] = colorId;
        combinedTakenColors[colorId] = true;
      }
    });

    return allColors.map(colorId => ({
      id: colorId,
      name: colorId.charAt(0).toUpperCase() + colorId.slice(1),
      hex: getColorHexById(colorId),
      isAvailable: !combinedTakenColors[colorId],
      selectedBy: Object.entries(combinedPlayerColors)
        .find(([_, color]) => color === colorId)?.[0],
    }));
  }, [hasEnhancedData, getEnhancedColorData, getAvailableColors, getTakenColors, getPlayerColors, playerColorSelections]);

  // Create player colors object for ColorSelector using enhanced or real-time data
  const playerColors = useMemo(() => {
    if (!currentRoom?.players) return {};

    // Get enhanced color data if available
    const enhancedColorData = getEnhancedColorData();
    const useEnhancedData = hasEnhancedData && enhancedColorData.source === 'enhanced';

    const colors: Record<string, {
      userId: string;
      username: string;
      isReady: boolean;
      colorId?: string;
      colorName?: string;
      colorHex?: string;
      balance?: number;
      insufficientBalance?: boolean;
    }> = {};

    currentRoom.players.forEach(player => {
      let colorId: string | undefined;

      if (useEnhancedData) {
        // Use enhanced game-specific data
        colorId = enhancedColorData.playerColors[player.userId];
      } else {
        // Fallback to real-time data
        const realTimePlayerColors = getPlayerColors();
        const legacyColorId = realTimePlayerColors[player.userId] || playerColorSelections[player.userId];

        // Ensure colorId is a string (handle cases where it might be an object)
        colorId = typeof legacyColorId === 'string' ? legacyColorId :
                  (legacyColorId && typeof legacyColorId === 'object' && 'colorId' in legacyColorId) ?
                  (legacyColorId as any).colorId : undefined;
      }

      const playerBalance = getPlayerBalance(player.userId);
      const hasInsufficientBalance = getPlayerInsufficientBalance(player.userId);

      colors[player.userId] = {
        userId: player.userId,
        username: player.username,
        isReady: playerReadyStates[player.userId] || false,
        colorId,
        colorName: colorId ? colorId.charAt(0).toUpperCase() + colorId.slice(1) : undefined,
        colorHex: colorId ? getColorHexById(colorId) : undefined,
        balance: playerBalance,
        insufficientBalance: hasInsufficientBalance,
      };
    });

    return colors;
  }, [currentRoom?.players, hasEnhancedData, getEnhancedColorData, playerColorSelections, playerReadyStates, getPlayerColors, getPlayerBalance, getPlayerInsufficientBalance]);

  // Calculate ready players with their selected colors using real-time data
  const readyPlayers: ReadyPlayer[] = useMemo(() => {
    if (!currentRoom?.players) return [];

    const realTimePlayerColors = getPlayerColors();

    return currentRoom.players
      .filter(player => {
        const isPlayerReady = playerReadyStates[player.userId] || false;
        const playerColorId = realTimePlayerColors[player.userId] || playerColorSelections[player.userId];
        // Ensure colorId is a string
        const colorIdString = typeof playerColorId === 'string' ? playerColorId :
                             (playerColorId && typeof playerColorId === 'object' && 'colorId' in playerColorId) ?
                             (playerColorId as any).colorId : undefined;
        return isPlayerReady && colorIdString;
      })
      .map(player => {
        const playerColorId = realTimePlayerColors[player.userId] || playerColorSelections[player.userId];
        // Ensure colorId is a string
        const colorIdString = typeof playerColorId === 'string' ? playerColorId :
                             (playerColorId && typeof playerColorId === 'object' && 'colorId' in playerColorId) ?
                             (playerColorId as any).colorId : undefined;
        return {
          userId: player.userId,
          username: player.username,
          colorId: colorIdString,
          colorHex: getColorHexById(colorIdString),
        };
      });
  }, [currentRoom?.players, playerColorSelections, playerReadyStates, getPlayerColors]);

  // Create all players with colors data for immediate visual feedback
  const playersWithColors: ReadyPlayer[] = useMemo(() => {
    if (!currentRoom?.players) return [];

    const realTimePlayerColors = getPlayerColors();
    return currentRoom.players
      .filter(player => {
        const playerColorId = realTimePlayerColors[player.userId] || playerColorSelections[player.userId];
        // Ensure colorId is a string
        const colorIdString = typeof playerColorId === 'string' ? playerColorId :
                             (playerColorId && typeof playerColorId === 'object' && 'colorId' in playerColorId) ?
                             (playerColorId as any).colorId : undefined;
        return colorIdString; // Only include players who have selected colors
      })
      .map(player => {
        const playerColorId = realTimePlayerColors[player.userId] || playerColorSelections[player.userId];
        // Ensure colorId is a string
        const colorIdString = typeof playerColorId === 'string' ? playerColorId :
                             (playerColorId && typeof playerColorId === 'object' && 'colorId' in playerColorId) ?
                             (playerColorId as any).colorId : undefined;
        return {
          userId: player.userId,
          username: player.username,
          colorId: colorIdString,
          colorHex: getColorHexById(colorIdString),
        };
      });
  }, [currentRoom?.players, playerColorSelections, getPlayerColors]);

  const handleColorSelect = async (colorId: string) => {
    if (gamePhase !== 'waiting' || isReady || isSelectingColor || isColorSelectionLoading) return;

    setIsSelectingColor(true);

    // Optimistically update the selected color immediately for instant visual feedback
    setSelectedColor(colorId);

    try {
      // Try enhanced color selection first, fallback to legacy
      const success = await enhancedSelectColor(colorId);

      if (!success) {
        // Fallback to legacy method
        await selectWheelColor(roomId, colorId);
      }

      // Success message is handled by the enhanced hook
      if (!success) {
        toast.success(`Selected ${colorId.charAt(0).toUpperCase() + colorId.slice(1)} color!`);
      }
    } catch (error: any) {
      // Revert the optimistic update on error
      setSelectedColor('');

      console.error('Failed to select color:', error);

      // Enhanced error handling with user-friendly messages for all game-service error codes
      let errorMessage = 'Failed to select color';

      switch (error.code) {
        case 'COLOR_ID_REQUIRED':
          errorMessage = 'Please select a color';
          break;
        case 'ROOM_NOT_FOUND':
          errorMessage = 'Room not found. Please refresh and try again';
          break;
        case 'PLAYER_NOT_IN_ROOM':
          errorMessage = 'You must be in the room to select a color';
          break;
        case 'COLOR_ALREADY_SELECTED':
          errorMessage = 'This color is already taken by another player';
          break;
        case 'COLOR_STATE_ERROR':
          errorMessage = 'Unable to update color state. Please try again';
          break;
        case 'COLOR_UPDATE_ERROR':
          errorMessage = 'Failed to save color selection. Please try again';
          break;
        case 'WHEEL_COLOR_SELECTION_TIMEOUT':
          errorMessage = 'Color selection timed out. Please check your connection and try again';
          break;
        case 'VALIDATION_ERROR':
          errorMessage = error.message || 'Invalid color selection';
          break;
        case 'NOT_IN_ROOM':
          errorMessage = 'You must be in a room to select a color';
          break;
        case 'COLOR_ALREADY_TAKEN':
          errorMessage = 'This color is already taken by another player';
          break;
        default:
          if (error.message.includes('timeout')) {
            errorMessage = 'Color selection timed out. Please check your connection and try again';
          } else if (error.message.includes('Socket not connected')) {
            errorMessage = 'Connection lost. Please refresh and try again';
          } else {
            errorMessage = error.message || 'Failed to select color. Please try again';
          }
      }

      toast.error(errorMessage);
    } finally {
      setIsSelectingColor(false);
    }
  };

  const handleToggleReady = async () => {
    if (!selectedColor) {
      toast.error('Please select a color first');
      return;
    }

    // Prevent multiple simultaneous ready toggle requests
    if (isTogglingReady) {
      return;
    }

    // Check if user has sufficient balance using real-time data
    const currentUserBalance = getPlayerBalance(currentUserId);
    const userBalance = currentUserBalance !== undefined ? currentUserBalance : user?.balance || 0;
    const betAmount = (currentRoom?.betAmount || 0) / 100; // Convert cents to dollars

    if (userBalance < betAmount) {
      toast.error(`Insufficient balance. You need $${betAmount.toFixed(2)} but only have $${userBalance.toFixed(2)}`);
      return;
    }

    setIsTogglingReady(true);

    try {
      await setPlayerReady(roomId, !isReady);
      // Don't update local state - let the socket event update the store
      toast.success(isReady ? 'Marked as not ready' : 'Marked as ready!');
    } catch (error: any) {
      console.error('Failed to set ready state:', error);

      // Enhanced error handling with detailed balance information
      let errorMessage = 'Failed to update ready status';

      switch (error.code) {
        case 'VALIDATION_ERROR':
          errorMessage = error.message || 'Invalid ready request';
          break;
        case 'INSUFFICIENT_BALANCE':
          // Check if error has detailed balance information
          if (error.details && error.details.currentBalance !== undefined && error.details.requiredAmount !== undefined) {
            const currentBalance = error.details.currentBalance / 100; // Convert cents to dollars
            const requiredAmount = error.details.requiredAmount / 100;
            const deficit = error.details.deficit / 100;
            errorMessage = `Insufficient balance. You need $${requiredAmount.toFixed(2)} but only have $${currentBalance.toFixed(2)} (short by $${deficit.toFixed(2)})`;
          } else {
            errorMessage = error.message || 'Insufficient balance to participate';
          }
          break;
        case 'NOT_IN_ROOM':
          errorMessage = 'You must be in a room to mark ready';
          break;
        case 'BALANCE_VALIDATION_FAILED':
          errorMessage = 'Unable to verify your balance. Please try again or contact support';
          break;
        case 'BET_DEDUCTION_FAILED':
          errorMessage = 'Failed to process bet amount. Please try again';
          break;
        case 'PLAYER_NOT_IN_ROOM':
          errorMessage = 'You are no longer in this room';
          break;
        case 'ROOM_NOT_FOUND':
          errorMessage = 'Room no longer exists';
          break;
        default:
          if (error.message && error.message.includes('Insufficient balance')) {
            errorMessage = error.message;
          } else {
            errorMessage = error.message || 'Failed to update ready status';
          }
      }

      toast.error(errorMessage, {
        duration: 5000, // Show longer for balance errors
        icon: error.code === 'INSUFFICIENT_BALANCE' ? '💰' : '❌',
      });
    } finally {
      setIsTogglingReady(false);
    }
  };

  // Reset all state when entering a new room
  useEffect(() => {
    if (currentRoom) {
      // Reset all local state to sync with new room
      setSelectedColor('');
      setGamePhase('waiting');
      setCountdown(10);
      setIsSpinning(false);
      setWinnerColor('');
      setIsSelectingColor(false);

      // Log room change for debugging
      console.log('Prize Wheel room changed - resetting local state:', {
        roomId: currentRoom.id,
        roomName: currentRoom.name,
        playerCount: currentRoom.playerCount,
        gameType: currentRoom.gameType,
      });
    }
  }, [currentRoom?.id]); // Only trigger when room ID changes

  // Sync selected color with room data when joining
  useEffect(() => {
    if (currentRoom && currentUserId) {
      // Get the current user's color selection from the store
      const userColorSelection = playerColorSelections[currentUserId];
      const realTimeUserColor = getPlayerColors()[currentUserId];

      // Use real-time data first, then fall back to store data
      const currentUserColor = realTimeUserColor || userColorSelection;

      if (currentUserColor && currentUserColor !== selectedColor) {
        setSelectedColor(currentUserColor);
        console.log('Synced selected color from room data:', {
          userId: currentUserId,
          colorId: currentUserColor,
          source: realTimeUserColor ? 'real-time' : 'store',
        });
      }
    }
  }, [currentRoom?.id, currentUserId, playerColorSelections, getPlayerColors, selectedColor]);

  // Update state based on socket events and room info
  useEffect(() => {
    // Handle room status and countdown from room_info_updated event
    if (currentRoom) {
      switch (currentRoom.status) {
        case 'STARTING':
        case 'ROOM_STATUS_STARTING':
          setGamePhase('countdown');
          // Get countdown from room state if available
          const roomCountdown = (currentRoom as any).countdown;
          if (roomCountdown !== undefined && roomCountdown !== null) {
            setCountdown(roomCountdown);
          }
          setIsSpinning(false);
          setWinnerColor('');
          break;
        case 'PLAYING':
        case 'ROOM_STATUS_PLAYING':
          setGamePhase('spinning');
          setIsSpinning(false); // Will be set to true when wheel_spinning event arrives
          setWinnerColor('');
          break;
        case 'FINISHED':
        case 'ROOM_STATUS_FINISHED':
          setGamePhase('results');
          setIsSpinning(false);
          break;
        default:
          setGamePhase('waiting');
          setIsSpinning(false);
          setWinnerColor('');
      }
    }

    // Handle game events (legacy support)
    if (currentGame) {
      switch (currentGame.status) {
        case 'starting':
          setGamePhase('countdown');
          setCountdown(currentGame.data?.countdown || 10);
          setIsSpinning(false);
          setWinnerColor('');
          break;
        case 'playing':
          setGamePhase('spinning');
          setIsSpinning(false); // Will be set to true when wheel_spinning event arrives
          setWinnerColor('');
          break;
        case 'spinning':
          setGamePhase('spinning');
          setIsSpinning(currentGame.data?.isSpinning || true);
          setWinnerColor('');
          break;
        case 'results':
          setGamePhase('results');
          setIsSpinning(false);
          // Handle both old and new data structures
          const winningColor = currentGame.data?.winningColor ||
                              currentGame.data?.winnerColor ||
                              currentGame.data?.winner?.wheelColor;
          if (winningColor) {
            setWinnerColor(winningColor);
          }
          break;
        case 'finished':
          setGamePhase('results');
          setIsSpinning(false);
          const finishedWinningColor = currentGame.data?.winningColor ||
                                     currentGame.data?.winnerColor ||
                                     currentGame.data?.winner?.wheelColor;
          if (finishedWinningColor) {
            setWinnerColor(finishedWinningColor);
          }
          break;
      }
    }
  }, [currentGame, currentRoom]);

  // Update player's selected color from store (prioritize real-time data)
  useEffect(() => {
    const realTimePlayerColors = getPlayerColors();
    const playerColor = realTimePlayerColors[currentUserId] || playerColorSelections[currentUserId];
    // Ensure playerColor is a string
    const colorIdString = typeof playerColor === 'string' ? playerColor :
                         (playerColor && typeof playerColor === 'object' && 'colorId' in playerColor) ?
                         (playerColor as any).colorId : undefined;

    if (colorIdString && colorIdString !== selectedColor) {
      setSelectedColor(colorIdString);
    }
  }, [playerColorSelections, currentUserId, getPlayerColors, selectedColor]);

  const canSelectColor = gamePhase === 'waiting' && !isReady;
  const canToggleReady = gamePhase === 'waiting' && !!selectedColor;

  return (
    <div className={`space-y-4 ${className}`}>

      {/* Game Phase Indicator */}
      {gamePhase !== 'waiting' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center justify-center space-x-2">
            {gamePhase === 'countdown' && (
              <>
                <Clock className="w-4 h-4 text-yellow-600" />
                <span className="text-yellow-800 font-medium">
                  Game starts in {countdown}s
                </span>
              </>
            )}
            {gamePhase === 'spinning' && (
              <>
                <Target className="w-4 h-4 text-yellow-600 animate-spin" />
                <span className="text-yellow-800 font-medium">
                  Wheel spinning...
                </span>
              </>
            )}
            {gamePhase === 'results' && (
              <>
                <Trophy className="w-4 h-4 text-yellow-600" />
                <span className="text-yellow-800 font-medium">
                  Results are in!
                </span>
              </>
            )}
          </div>
        </div>
      )}

      {/* Game Results */}
      {gamePhase === 'results' && currentGame?.data && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <Trophy className="w-6 h-6 text-green-600" />
            <h3 className="text-xl font-bold text-green-800">Game Results</h3>
          </div>

          <div className="space-y-4">
            {/* Winning Color */}
            {winnerColor && (
              <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-green-200">
                <span className="font-medium text-gray-700">Winning Color:</span>
                <div className="flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded-full border-2 border-gray-300"
                    style={{ backgroundColor: getColorHexById(winnerColor) }}
                  ></div>
                  <span className="font-bold text-green-800 capitalize">{winnerColor}</span>
                </div>
              </div>
            )}

            {/* Prize Information */}
            {currentGame.data.results && (
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-white rounded-lg border border-green-200">
                  <div className="text-sm text-gray-600">Total Prize Pool</div>
                  <div className="text-lg font-bold text-green-800">
                    ${currentGame.data.results.prizePool || 0}
                  </div>
                </div>
                <div className="p-4 bg-white rounded-lg border border-green-200">
                  <div className="text-sm text-gray-600">Number of Winners</div>
                  <div className="text-lg font-bold text-green-800">
                    {currentGame.data.results.winners || 0}
                  </div>
                </div>
              </div>
            )}

            {/* Winner Information */}
            {currentGame.data.winner && (
              <div className="p-4 bg-white rounded-lg border border-green-200">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-700">Winner:</span>
                  <div className="text-right">
                    <div className="font-bold text-green-800">{currentGame.data.winner.userId}</div>
                    <div className="text-sm text-gray-600">
                      Won ${currentGame.data.winner.winAmount || 0}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Main Game Area - Grid Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Prize Wheel */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-2 mb-4">
            <Target className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-medium text-gray-900">Prize Wheel</h3>
          </div>
          <div className="flex justify-center">
            <PrizeWheel
              availableColors={availableColors}
              selectedColor={selectedColor}
              onColorSelect={handleColorSelect}
              isSpinning={isSpinning}
              finalAngle={currentGame?.data?.animation?.finalPosition || 0}
              winnerColor={winnerColor}
              disabled={!canSelectColor}
              size={380} // Use the enhanced larger size
              readyPlayers={readyPlayers}
              playersWithColors={playersWithColors}
              currentUserId={currentUserId}
              currentUserName={user?.username}
            />
          </div>
        </div>

        {/* Right Column - Game Controls */}
        <div className="space-y-4">
          {/* Color Selection & Ready */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-4 h-4 rounded-full bg-gradient-to-r from-red-500 to-blue-500"></div>
              <h3 className="font-medium text-gray-900">Select Color & Ready</h3>
            </div>

            {/* Enhanced Debug Info - Temporary */}
            <div className="bg-yellow-50 border border-yellow-200 rounded p-2 text-xs mb-3">
              <div>Enhanced Data: {hasEnhancedData ? '✅ Available' : '❌ Not Available'}</div>
              <div>Data Source: {getEnhancedColorData().source}</div>
              <div>Selected Color: {selectedColor || 'None'}</div>
              <div>Player Colors: {JSON.stringify(Object.fromEntries(Object.entries(playerColors).map(([, p]) => [p.username, p.colorId])))}</div>
              <div>Taken Colors: {availableColors.filter(c => !c.isAvailable).map(c => `${c.id}(${c.selectedBy})`).join(', ')}</div>
              <div>Ready Players: {readyPlayers.map(p => `${p.username}(${p.colorId})`).join(', ')}</div>

              {/* Enhanced Color Selection Statistics */}
              {(enhancedAvailableColors.length > 0 || Object.keys(enhancedTakenColors).length > 0) && (
                <div className="mt-2 pt-2 border-t border-blue-300 bg-blue-50">
                  <div className="font-semibold text-blue-800">🎨 Enhanced Color Selection System:</div>
                  <div>Available: {colorStatistics.availableCount} | Taken: {colorStatistics.takenCount}</div>
                  <div>Selection Rate: {colorStatistics.selectionRate}% ({colorStatistics.playersWithColors}/{colorStatistics.totalPlayers})</div>
                  <div>Current User: {enhancedCurrentUserColor ? `${enhancedCurrentUserColor.colorName} (${enhancedCurrentUserColor.colorId})` : 'None'}</div>
                  <div>Last Update: {colorLastUpdate ? new Date(colorLastUpdate).toLocaleTimeString() : 'Never'}</div>
                  <div>Loading: {isColorSelectionLoading ? '⏳' : '✅'}</div>
                </div>
              )}

              {hasEnhancedData && prizeWheelData && (
                <div className="mt-2 pt-2 border-t border-yellow-300">
                  <div>Enhanced Color Selections: {Object.keys(prizeWheelData.colorSelections).length}</div>
                  <div>Enhanced Available Colors: {prizeWheelData.availableColors.length}</div>
                  <div>Enhanced Mappings: {Object.keys(prizeWheelData.playerColorMappings || {}).length}</div>
                </div>
              )}
            </div>

            <ColorSelector
              availableColors={availableColors}
              selectedColor={selectedColor}
              onColorSelect={handleColorSelect}
              disabled={!canSelectColor}
              loading={isSelectingColor}
              currentUserId={currentUserId}
              playerColors={playerColors}
              isReady={isReady}
              onToggleReady={handleToggleReady}
              canToggleReady={canToggleReady && !isTogglingReady}
              readyLoading={isTogglingReady}
            />
          </div>
        </div>
      </div>

      {/* Enhanced Color Selection Information */}
      {(Object.keys(colorAssignments).length > 0 || colorStatistics.takenCount > 0) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Left Column - Statistics and Assignments */}
          <div className="space-y-4">
            {/* Color Selection Statistics */}
            <ColorSelectionStats
              statistics={colorStatistics}
              assignments={colorAssignments}
              currentPlayerColor={currentPlayerColorData}
              lastUpdate={colorLastUpdate || undefined}
            />

            {/* Player Color Assignments */}
            <PlayerColorAssignments
              assignments={colorAssignments}
              selectedColors={selectedColorsData}
              currentUserId={currentUserId}
              compact={true}
            />
          </div>

          {/* Right Column - Live Activity Feed */}
          <div>
            <ColorSelectionActivity maxItems={8} />
          </div>
        </div>
      )}

      {/* Game Information */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-800 mb-2">How to Play</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <p>• Select a color to participate in the wheel</p>
          <p>• Mark ready when you're prepared to play</p>
          <p>• Game starts when enough players are ready</p>
          <p>• Watch the wheel spin to see who wins!</p>
        </div>
      </div>
    </div>
  );
};

export default PrizeWheelReadySection;
