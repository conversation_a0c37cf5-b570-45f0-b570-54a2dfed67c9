// Socket Event Types
export interface SocketEvents {
  // Connection events
  connect: () => void;
  disconnect: (reason: string) => void;
  connect_error: (error: Error) => void;
  connect_ack: (data: ConnectAckData) => void;

  // Lobby events
  subscribe_lobby: (callback?: (response: SocketResponse) => void) => void;
  unsubscribe_lobby: (callback?: (response: SocketResponse) => void) => void;
  room_list_updated: (data: RoomListUpdateData) => void;

  // Room events
  join_room: (data: JoinRoomData, callback?: (response: SocketResponse) => void) => void;
  leave_room: (data: LeaveRoomData, callback?: (response: SocketResponse) => void) => void;
  room_info_updated: (data: RoomInfoUpdatedData) => void;
  player_joined: (data: PlayerJoinedData) => void;
  player_left: (data: PlayerLeftData) => void;
  player_ready: (data: PlayerReadyData, callback?: (response: SocketResponse) => void) => void;
  player_unready: (data: PlayerReadyData, callback?: (response: SocketResponse) => void) => void;
  player_ready_status: (data: PlayerReadyStatusData) => void;
  player_ready_changed: (data: PlayerReadyStatusData) => void;

  // Prize Wheel color selection events
  select_wheel_color: (data: WheelColorSelectionData, callback?: (response: SocketResponse) => void) => void;
  select_color_spec: (data: { payload: { color: string } }, callback?: (response: SocketResponse) => void) => void;
  wheel_color_selected: (data: WheelColorStatusData) => void;
  room_color_state_updated: (data: RoomColorStateData) => void;
  colorStateUpdate: (data: ColorStateUpdateData) => void;

  // New Prize Wheel Color Selection Events
  available_colors: (event: AvailableColorsEvent) => void;
  color_selection_update: (event: ColorSelectionUpdateEvent) => void;
  color_state_sync: (event: ColorStateSyncEvent) => void;
  color_selection_updated: (event: ColorSelectionUpdatedEvent) => void;

  // Enhanced room events with real data
  room_update_spec: (data: RoomUpdateSpecData) => void;

  // Game events
  game_starting: (data: GameStartingData) => void;
  game_started: (data: GameStartedData) => void;
  game_update: (data: GameUpdateData) => void;
  game_finished: (data: GameFinishedData) => void;
  game_cancelled: (data: GameCancelledData) => void;

  // Chat events (if implemented)
  chat_message: (data: ChatMessageData) => void;
  chat_message_received: (data: ChatMessageData) => void;

  // System events
  notification: (data: NotificationData) => void;
  error: (data: SocketErrorData) => void;
  ping: (callback?: (response: PingResponse) => void) => void;
  pong: (data: PongData) => void;

  // Balance events
  balance_updated: (data: BalanceUpdateData) => void;

  // Admin events
  admin_notification: (data: AdminNotificationData) => void;
  system_maintenance: (data: SystemMaintenanceData) => void;
}

// Socket Data Types
export interface ConnectAckData {
  userId: string;
  username: string;
  sessionId: string;
  serverTime: string;
  balance: number;
}

export interface JoinRoomData {
  roomId: string;
  password?: string;
  betAmount?: number;
  enhanced?: boolean;
  timestamp?: string;
}

export interface LeaveRoomData {
  roomId: string;
  reason?: 'voluntary' | 'disconnected' | 'kicked';
}

// Base room data structure for updates
export interface RoomUpdateData {
  id: string;
  name: string;
  gameType: string;
  playerCount: number;
  maxPlayers: number;
  status: string;
  betAmount: number;
  currency?: string;
  isPrivate?: boolean;
  createdAt: string;
  updatedAt?: string;
}

// Room list update data - supports both single room updates and initial bulk load
export type RoomListUpdateData =
  | {
      action: 'initial_load' | 'room_list';
      rooms: RoomUpdateData[];
      timestamp?: string;
      error?: string;
    }
  | {
      action: 'created' | 'updated' | 'deleted' | 'player_count_changed' | 'room_added' | 'room_removed';
      room: RoomUpdateData;
      timestamp?: string;
    }
  | {
      action: 'player_joined_room';
      rooms: RoomUpdateData[];
      joinedRoomId: string;
      timestamp?: string;
    }
  | {
      action: 'subscribe' | 'unsubscribe';
      count: number;
      rooms: RoomUpdateData[];
      socketId?: string;
      userId?: string;
      username?: string;
      source?: string;
      timestamp?: string;
      error?: string;
    };

export interface PlayerJoinedData {
  player: {
    userId: string;
    username: string;
    avatar?: string;
    joinedAt: string;
  };
  roomState: {
    playerCount: number;
    maxPlayers: number;
    readyCount: number;
  };
}

export interface PlayerLeftData {
  playerId: string;
  username: string;
  reason: 'voluntary' | 'disconnected' | 'kicked' | 'timeout';
  roomState: {
    playerCount: number;
    maxPlayers: number;
    readyCount: number;
  };
}

export interface PlayerReadyData {
  roomId: string;
  isReady: boolean;
}

export interface PlayerReadyStatusData {
  playerId: string;
  username: string;
  isReady: boolean;
  readyCount: number;
  totalPlayers: number;
  canStartGame?: boolean;
  prizePool?: number;
  gameType?: 'PRIZEWHEEL' | 'AMIDAKUJI' | 'GAME_TYPE_PRIZE_WHEEL' | 'GAME_TYPE_AMIDAKUJI';
  betAmount?: number;
  position?: number; // For Amidakuji position selection
  wheelColor?: string; // For Prize Wheel color selection
}

export interface WheelColorSelectionData {
  roomId: string;
  colorId: string;
}

export interface WheelColor {
  id: string;
  name: string;
  hex: string;
  isAvailable: boolean;
  selectedBy?: string; // playerId if selected
}

// Amidakuji specific types
export interface AmidakujiPositionSelectionData {
  roomId: string;
  position: number;
}

export interface AmidakujiPositionStatusData {
  playerId: string;
  username: string;
  position: number;
  isReady: boolean;
  timestamp: string;
}

export interface PositionStateUpdateData {
  roomId: string;
  participantCount: number;
  prizePool: number;
  players: Record<string, {
    position: number;
    ready: boolean;
    balance: number;
    insufficientBalance: boolean;
  }>;
  availablePositions: number[];
  takenPositions: Record<number, boolean>;
  playerPositions: Record<string, number>;
}

export interface RoomPositionStateData {
  roomId: string;
  availablePositions: number[];
  takenPositions: Record<number, boolean>;
  playerPositions: Record<string, number>;
  playerReadyStates: Record<string, boolean>;
  timestamp: string;
}

export interface AmidakujiPatternData {
  gameId: string;
  pattern: {
    columns: number;
    rows: number;
    horizontalLines: Array<{
      row: number;
      fromColumn: number;
      toColumn: number;
    }>;
  };
  participants: Array<{
    userId: string;
    startColumn: number;
  }>;
  winnerPath: number[];
  winnerUserId: string;
}

export interface AmidakujiPathTracingData {
  gameId: string;
  tracing: {
    currentStep: number;
    totalSteps: number;
    paths: Array<{
      userId: string;
      currentPosition: { row: number; column: number };
      pathHistory: Array<{ row: number; column: number }>;
    }>;
  };
}

export interface WheelColorStatusData {
  playerId: string;
  username: string;
  colorId: string;
  colorName: string;
  colorHex: string;
  roomId: string;
  availableColors: WheelColor[];
  playerColors?: Record<string, {
    userId: string;
    username: string;
    isReady: boolean;
    colorId?: string;
    colorName?: string;
    colorHex?: string;
  }>;
  totalPlayers: number;
  timestamp?: string;
}

export interface RoomColorStateData {
  roomId: string;
  selectorUserId: string;
  selectorUsername: string;
  selectedColorId: string;
  availableColors: WheelColor[];
  playerColors: Record<string, {
    userId: string;
    username: string;
    isReady: boolean;
    colorId?: string;
    colorName?: string;
    colorHex?: string;
  }>;
  totalPlayers: number;
  timestamp: string;
}

// Enhanced color state update data from backend
export interface ColorStateUpdateData {
  roomId: string;
  playerColors: Record<string, string>; // userId -> colorId mapping
  availableColors: string[]; // Array of available color IDs
  takenColors: Record<string, boolean>; // colorId -> taken status
  timestamp: string;
}

// Enhanced room update specification data with real player info
export interface RoomUpdateSpecData {
  roomId: string;
  gameState: 'WAITING' | 'STARTING' | 'PLAYING' | 'END';
  participantCount: number;
  prizePool: number;
  players: Record<string, {
    id: string;
    name: string;
    color: string;
    ready: boolean;
    balance: number;
    isParticipating: boolean;
    insufficientBalance: boolean;
  }>;
  availableColors: string[];
  takenColors: Record<string, boolean>;
  gameConfig?: {
    maxPlayers: number;
    betAmount: number;
    gameType: string;
  };
  timestamp: string;
}

// Game-specific data structures for enhanced room info
export interface GameSpecificData {
  gameType: string;
  [key: string]: any;
}

export interface PrizeWheelGameData extends GameSpecificData {
  gameType: 'prizewheel' | 'prize_wheel';
  colorSelections: Record<string, string>; // userId -> colorId mapping
  availableColors: string[]; // Array of available color IDs
  playerColorMappings: Record<string, {
    colorId: string;
    selectedAt: string;
  }>; // userId -> detailed color info
  colorSelectionTimestamps: Record<string, string>; // userId -> timestamp mapping
}

export interface AmidakujiGameData extends GameSpecificData {
  gameType: 'amidakuji';
  positionSelections: Record<string, number>; // userId -> position mapping
  availablePositions: number[]; // Array of available positions
  playerPositionMappings: Record<string, {
    position: number;
    selectedAt: string;
  }>; // userId -> detailed position info
}

// Comprehensive room info updated event data structure with game-specific data
// Supports both legacy format and new comprehensive format
export interface RoomInfoUpdatedData {
  // Legacy format fields
  room?: {
    id: string;
    name: string;
    gameType: 'prizewheel' | 'amidakuji';
    status: 'waiting' | 'starting' | 'playing' | 'end' | 'finished';
    playerCount: number;
    maxPlayers: number;
    betAmount: number;
    prizePool: number;
    isPrivate: boolean;
    createdAt: string;
    updatedAt: string;
  };
  roomState?: {
    playerCount: number;
    readyCount: number;
    canStartGame: boolean;
    prizePool: number;
    gameInProgress: boolean;
    countdown?: number | null;
  };
  players?: Array<{
    userId: string;
    username: string;
    betAmount: number;
    isReady: boolean;
    joinedAt: string;
    position: number;
    balance?: number;
    insufficientBalance?: boolean;
    colorId?: string;
    colorHex?: string;
  }>;
  gameConfig?: {
    betAmount: number;
    gameType: 'prizewheel' | 'amidakuji';
    maxPlayers: number;
    minPlayers: number;
    settings?: Record<string, any>;
  };
  gameSpecificData?: PrizeWheelGameData | AmidakujiGameData | GameSpecificData;
  timestamp?: string;

  // New comprehensive format fields
  roomId?: string;
  reason?: string;
  game?: {
    allColorsSelected: boolean;
    canStart: boolean;
    type: string;
  };
  metadata?: {
    action: string;
    userId: string;
    username: string;
  };
  // New players format with enhanced structure
  players?: {
    colorMappings: Record<string, any>;
    list: Array<{
      betAmount: number;
      isReady: boolean;
      joinedAt: string;
      position: number;
      userId: string;
      username: string;
    }>;
    total: number;
    withColors: number;
  };
  // New room format with snake_case fields
  room?: {
    bet_amount: number;
    currency: string;
    current_players: number;
    game_type: string;
    has_password: boolean;
    has_space: boolean;
    id: string;
    is_featured: boolean;
    is_private: boolean;
    max_players: number;
    min_players: number;
    name: string;
    prize_pool: number;
    status: string;
  };
}

export interface GameStartingData {
  roomId: string;
  gameId: string;
  countdown: number;
  startTime: string;
  players: Array<{
    userId: string;
    username: string;
    position: number;
    betAmount: number;
  }>;
}

export interface GameStartedData {
  roomId: string;
  gameId: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  startedAt: string;
  estimatedDuration: number;
  gameConfig: Record<string, any>;
  players: Array<{
    userId: string;
    username: string;
    position: number;
    betAmount: number;
  }>;
}

export interface GameUpdateData {
  roomId: string;
  gameId: string;
  updateType: 'progress' | 'state_change' | 'player_action';
  data: Record<string, any>;
  timestamp: string;
}

export interface GameFinishedData {
  roomId: string;
  gameId: string;
  result: {
    winnerId: string;
    winnerUsername: string;
    winAmount: number;
    gameData: Record<string, any>;
  };
  finishedAt: string;
  duration: number;
  nextGameCountdown?: number;
}

export interface GameCancelledData {
  roomId: string;
  gameId: string;
  reason: string;
  refundAmount?: number;
  cancelledAt: string;
}

export interface ChatMessageData {
  roomId: string;
  messageId: string;
  senderId: string;
  senderUsername: string;
  message: string;
  timestamp: string;
  type: 'text' | 'system' | 'game_action';
}

export interface NotificationData {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  persistent?: boolean;
  actionUrl?: string;
}

export interface SocketErrorData {
  code: string;
  message: string;
  timestamp: string;
  context?: Record<string, any>;
}

export interface PingResponse {
  timestamp: string;
  latency: number;
}

export interface PongData {
  timestamp: string;
  latency: number;
}

export interface BalanceUpdateData {
  userId: string;
  username: string;
  balance: number;
  previousBalance: number;
  transaction: {
    id: string;
    transactionId: string;
    type: 'deposit' | 'withdrawal' | 'bet' | 'win' | 'refund' | 'admin_deposit' | 'admin_withdrawal';
    amount: number;
    description: string;
    gameId?: string;
    roomId?: string;
  };
  timestamp: string;
}

// Enhanced error data with validation details
export interface EnhancedSocketErrorData extends SocketErrorData {
  details?: {
    errors?: Array<{
      field: string;
      message: string;
    }>;
    validationErrors?: Record<string, string>;
  };
}

export interface AdminNotificationData {
  id: string;
  type: 'system' | 'maintenance' | 'announcement';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  expiresAt?: string;
}

export interface SystemMaintenanceData {
  scheduled: boolean;
  startTime: string;
  estimatedDuration: number;
  reason: string;
  affectedServices: string[];
}

// Socket Response Types
export interface SocketResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  details?: any;
}

// Connection State Types
export type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';

export interface ConnectionInfo {
  state: ConnectionState;
  connectedAt?: Date;
  lastPing?: number;
  reconnectAttempts: number;
  error?: string;
}

// Room State Types
export interface RoomState {
  id: string;
  name: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI' | 'GAME_TYPE_PRIZE_WHEEL' | 'GAME_TYPE_AMIDAKUJI';
  status: 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED' | 'ROOM_STATUS_WAITING' | 'ROOM_STATUS_STARTING' | 'ROOM_STATUS_PLAYING' | 'ROOM_STATUS_FINISHED';
  players: Array<{
    userId: string;
    username: string;
    avatar?: string;
    position: number;
    isReady: boolean;
    isHost: boolean;
    balance?: number;
    insufficientBalance?: boolean;
    colorId?: string;
    colorHex?: string;
    status?: string; // New field for player status
    betAmount?: string | number; // New field for bet amount
    joinedAt?: string | Date; // New field for join timestamp
  }>;
  playerCount: number;
  maxPlayers: number;
  minPlayers?: number; // New field for minimum players
  readyCount: number;
  canStartGame: boolean;
  betAmount?: number;
  minBet?: number;
  maxBet?: number;
  prizePool?: number;
  participantCount?: number;
  countdown?: number | null;
  // New fields from updated event format
  isPrivate?: boolean;
  autoStart?: boolean;
  canJoin?: boolean;
  createdAt?: string | Date;
  lastActivity?: string | Date;
  currentGame?: {
    id: string;
    status: string;
    startedAt?: string;
    estimatedEndAt?: string;
  };
  // Real-time color state
  colorState?: {
    playerColors: Record<string, string>;
    availableColors: string[];
    takenColors: Record<string, boolean>;
  };
}

// New Prize Wheel Color Selection Event Interfaces
export interface AvailableColorsEvent {
  data: {
    roomId: string;
    availableColors: Array<{
      id: string;
      name: string;
      hex: string;
    }>;
    takenColors: Array<{
      id: string;
      name: string;
      hex: string;
      takenBy: {
        userId: string;
        username: string;
      };
    }>;
    availableCount: number;
    takenCount: number;
    timestamp: string;
  };
}

export interface ColorSelectionUpdateEvent {
  data: {
    roomId: string;
    player: {
      userId: string;
      username: string;
      selectedColor?: {
        id: string;
        name: string;
        hex: string;
      };
      unselectedColor?: {
        id: string;
        name: string;
        hex: string;
      };
    };
    action: 'selected' | 'unselected';
    availableColors: Array<{
      id: string;
      name: string;
      hex: string;
    }>;
    takenColors: Array<{
      id: string;
      name: string;
      hex: string;
      takenBy: {
        userId: string;
        username: string;
      };
    }>;
    timestamp: string;
  };
}

export interface ColorStateSyncEvent {
  data: {
    roomId: string;
    playerColorMappings: Record<string, {
      userId: string;
      username: string;
      color: {
        id: string;
        name: string;
        hex: string;
      };
      selectedAt: string;
    }>;
    playerList: Array<{
      userId: string;
      username: string;
      isReady: boolean;
      hasSelectedColor: boolean;
      selectedColor?: {
        id: string;
        name: string;
        hex: string;
      };
    }>;
    availableColors: Array<{
      id: string;
      name: string;
      hex: string;
    }>;
    takenColors: Array<{
      id: string;
      name: string;
      hex: string;
      takenBy: {
        userId: string;
        username: string;
      };
    }>;
    statistics: {
      totalPlayers: number;
      playersWithColors: number;
      selectionRate: number;
      availableCount: number;
      takenCount: number;
    };
    timestamp: string;
  };
}

// New comprehensive color selection updated event
export interface ColorSelectionUpdatedEvent {
  action: 'color_selected' | 'color_unselected';
  colorState: {
    assignments: Record<string, {
      color: {
        hex: string;
        id: string;
        name: string;
      };
      selectedAt: string;
      userId: string;
      username: string;
    }>;
    availableColors: Array<{
      hex: string;
      id: string;
      name: string;
    }>;
    currentPlayerColor?: {
      hex: string;
      id: string;
      name: string;
    };
    selectedColors: Record<string, {
      selectedAt: string;
      userId: string;
      username: string;
    }>;
    statistics: {
      availableCount: number;
      selectionRate: number;
      takenCount: number;
      totalColors: number;
    };
    taken: Array<{
      hex: string;
      id: string;
      name: string;
    }>;
  };
  event: 'color_selection_updated';
  metadata: {
    gameType: string;
    reason: string;
    triggeredBy: string;
  };
  player: {
    color: {
      hex: string;
      id: string;
      name: string;
    };
    selectedAt: string;
    userId: string;
    username: string;
  };
  roomId: string;
  timestamp: string;
}
