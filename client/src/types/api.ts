// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    status_code: number;
    timestamp: string;
    request_id: string;
  };
  meta?: {
    timestamp: string;
    request_id: string;
  };
}

// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  profile: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
    dateOfBirth?: string;
    country?: string;
  };
  balance: number;
  currency?: string;
  roles?: string[];
  permissions?: string[];
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  lastLoginAt?: string;
  statistics?: {
    gamesPlayed: number;
    gamesWon: number;
    totalWinnings: number;
    totalLosses: number;
  };
}

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  country: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Room Types
export interface Room {
  id: string;
  name: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  status: 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED';
  creatorId?: string;
  // Flat structure to match backend response
  maxPlayers: number;
  currentPlayerCount: number;
  currentPlayers?: number; // Alternative field name for compatibility
  playerCount?: number; // Alternative field name for compatibility
  betAmount: number;
  currency: string;
  gameDuration?: number;
  isPrivate: boolean;
  password?: string;
  players: Player[];
  createdAt: string;
  updatedAt: string;
  prizePool?: number; // Total prize pool for the room
  // Optional nested config for backward compatibility
  config?: {
    maxPlayers: number;
    betAmount: number;
    currency: string;
    gameDuration: number;
    gameSpecificConfig: Record<string, any>;
  };
}

export interface Player {
  id: string;
  userId: string;
  username: string;
  avatar?: string;
  position: number;
  isReady: boolean;
  betAmount: number;
  joinedAt: string;
  isHost?: boolean;
}

export interface CreateRoomRequest {
  name: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  betAmount: number;
  currency: string;
  maxPlayers: number;
  isPrivate: boolean;
  password?: string;
}

export interface JoinRoomRequest {
  roomId: string;
  password?: string;
}

export interface GetRoomsRequest {
  page?: number;
  limit?: number;
  gameType?: string;
  status?: string;
  minBet?: number;
  maxBet?: number;
}

export interface RoomsResponse {
  rooms: Room[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Game Types
export interface Game {
  id: string;
  roomId: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  status: 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED';
  players: Player[];
  config: Record<string, any>;
  result?: GameResult;
  startedAt?: string;
  finishedAt?: string;
  duration?: number;
}

export interface GameResult {
  winnerId: string;
  winnerUsername: string;
  winAmount: number;
  gameData: Record<string, any>;
  timestamp: string;
}

// Transaction Types
export interface Transaction {
  id: string;
  userId: string;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'BET' | 'WIN' | 'REFUND';
  amount: number;
  currency: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  description: string;
  gameId?: string;
  roomId?: string;
  createdAt: string;
  updatedAt: string;
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  status_code: number;
  timestamp: string;
  request_id: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
